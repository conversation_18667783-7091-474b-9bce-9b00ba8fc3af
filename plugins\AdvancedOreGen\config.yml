# Here you can define worlds where the generator should not work
disabled-worlds:
- none
# AFK mode: Players who stay more than 20 minutes afk would get the defined afk generator
# remove the '#' to enable this feature.
# afk-generator-name: afk
# afk-after-x-seconds: 1200

# command for showing and choosing a generator (permission: advancedoregen.showgenerator)
show-generator-command: generator
# whether the /generator command above can only be used on the own island
show-generator-only-on-own-island: false

# water <-> lava fix
# the water-lava-fix avoid generating ores when water is poured on lava directly and vice-versa
# set it to false for using custom ore gen's generator behavior
water-lava-fix: false

# fence generator, allows you to generate cobblestone by placing a fence block instead of lava.
fence-generator-enabled: true

# Allows to build fast generators (when obsidian is created in vanilla)
allow-fast-generators: true

# Enables custom blocks for the basalt generator
enable-basalt-generator: false

# By default, AOG tries to hook with your skyblock plugin, resulting in using the island owner's permission (or level) while gathering the right generator for your player.
# Forcing to use the vanilla hook means that the plugin would use the permission of the nearest player mining on the generator.
force-vanilla-hook: true

# Here you can define a skyblock plugin name that should be tried to load first (useful when having more than one skyblock plugin but wanting to hook into another one) 
force-hook: vanilla

generator20:
  label: Khoang San Tong Hop
  iconMaterial: NETHERITE_BLOCK
  permission: oregen.khoangsan_tonghop
  unlock_islandLevel: 0
  conditions:
    cobblestone: true
    basalt: false
  blocks:
  - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
    chance: 10.0
    material: ANCIENT_DEBRIS
  - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
    chance: 15.0
    material: DIAMOND_BLOCK
  - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
    chance: 15.0
    material: AMETHYST_BLOCK
  - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
    chance: 15.0
    material: LAPIS_BLOCK
  - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
    chance: 15.0
    material: REDSTONE_BLOCK
  - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
    chance: 15.0
    material: RAW_COPPER_BLOCK
  - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
    chance: 15.0
    material: QUARTZ_BLOCK
playerGenerators:
- fc909eaa-b036-3b15-8f1a-138e36bb5597:amethyst
- 47dc1d77-de2f-3ba7-b324-5007f9999e9b:amethyst
- c43dd693-8544-3e73-83a3-6a7883881bcd:emerald
- f60b5a47-1033-3c5d-80d6-173860ffd27b:emerald
- 56d82213-f742-3f2f-bf63-dc6d69ef5a2f:emerald
- d3df7716-b024-3638-b2ef-14f5a65670d0:emerald
- 40cd1830-3e91-3fac-8674-ada08bf2f04c:netherite
- 45b7b767-38c8-3c17-9dad-433cdf7a028a:emerald
- c78f2c4f-e092-3daf-a7ae-e3e7877453d0:amethyst
- a458dc8c-9cc8-3232-9f08-33beb577847f:amethyst
- 01d6361c-bccc-3c73-abd8-95a36683ffff:amethyst
- fc4a780e-c772-3a1f-9b10-ca4319844e6c:default
- e98ceab7-f1e4-3ecb-bcb9-d51365fdce55:amethyst
- c2027b1a-4a18-3dc5-ac28-fe9f7357291f:amethyst
- 43a0bb48-66ce-3cea-afb7-16629be19c04:emerald
- f80ee4e1-d978-3493-a7ef-a4ce086525df:emerald
generators:
  generator1:
    label: default
    iconMaterial: COBBLESTONE
    permission: oregen.default
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 30.0
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 30.0
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 20.0
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: LAPIS_ORE
  generator2:
    label: coal
    iconMaterial: COAL
    permission: oregen.coal
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 20.0
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 20.0
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 25.0
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 15.0
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 15.0
      material: LAPIS_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: IRON_ORE
  generator3:
    label: redstone
    iconMaterial: REDSTONE
    permission: oregen.redstone
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 15.0
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 15.0
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 19.0
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 19.0
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 19.0
      material: LAPIS_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: COPPER_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: IRON_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.0
      material: GOLD_ORE
  generator4:
    label: lapis
    iconMaterial: LAPIS_LAZULI
    permission: oregen.lapis
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 15.0
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 20.0
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 20.0
      material: LAPIS_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: COPPER_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: IRON_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: GOLD_ORE
  generator5:
    label: iron
    iconMaterial: RAW_IRON
    permission: oregen.iron
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 8.0
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 15.0
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 20.0
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 20.0
      material: LAPIS_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: IRON_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: COPPER_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 7.0
      material: GOLD_ORE
  generator6:
    label: gold
    iconMaterial: RAW_GOLD
    permission: oregen.gold
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 8.0
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 15.0
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 15.0
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 20.0
      material: LAPIS_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: COPPER_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: IRON_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: GOLD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.0
      material: DIAMOND_ORE
  generator7:
    label: diamond
    iconMaterial: DIAMOND
    permission: oregen.diamond
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 15.0
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 15.0
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 15.0
      material: LAPIS_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: COPPER_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: IRON_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: GOLD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: DIAMOND_ORE
  generator8:
    label: emerald
    iconMaterial: EMERALD
    permission: oregen.emerald
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.5
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.5
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.5
      material: LAPIS_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 15.0
      material: COPPER_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 15.0
      material: IRON_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: GOLD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: DIAMOND_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: EMERALD_ORE
  generator9:
    label: netherite
    iconMaterial: NETHERITE_INGOT
    permission: oregen.netherite
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.5
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.5
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.5
      material: LAPIS_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.5
      material: COPPER_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 15.0
      material: IRON_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: GOLD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: DIAMOND_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: EMERALD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.5
      material: ANCIENT_DEBRIS
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 1.5
      material: AMETHYST_BLOCK
  generator10:
    label: amethyst
    iconMaterial: AMETHYST_SHARD
    permission: oregen.amethyst
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: LAPIS_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.5
      material: COPPER_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.5
      material: IRON_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.5
      material: GOLD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: DIAMOND_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: EMERALD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: ANCIENT_DEBRIS
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: AMETHYST_BLOCK
  generator11:
    label: vipplus
    iconMaterial: GOLD_INGOT
    permission: oregen.vipplus
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 9.0
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 9.0
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 9.0
      material: LAPIS_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 9.0
      material: COPPER_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 11.0
      material: IRON_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 11.0
      material: GOLD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.0
      material: DIAMOND_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.0
      material: EMERALD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.0
      material: ANCIENT_DEBRIS
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.5
      material: AMETHYST_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 1.5
      material: COAL_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 1.5
      material: LAPIS_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 1.5
      material: REDSTONE_BLOCK
  generator12:
    label: mvp
    iconMaterial: GOLD_INGOT
    permission: oregen.mvp
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 7.0
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 7.0
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 7.0
      material: LAPIS_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 7.0
      material: COPPER_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 8.0
      material: IRON_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 9.0
      material: GOLD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.0
      material: DIAMOND_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.0
      material: EMERALD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 7.0
      material: ANCIENT_DEBRIS
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 4.5
      material: AMETHYST_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.0
      material: COAL_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.0
      material: LAPIS_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.0
      material: REDSTONE_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 1.5
      material: COPPER_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 1.3
      material: IRON_BLOCK
  generator13:
    label: mvpplus
    iconMaterial: GOLD_INGOT
    permission: oregen.mvpplus
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.0
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.0
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.0
      material: LAPIS_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.0
      material: COPPER_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 7.5
      material: IRON_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 7.5
      material: GOLD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.5
      material: DIAMOND_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 12.5
      material: EMERALD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 8.0
      material: ANCIENT_DEBRIS
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: AMETHYST_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.7
      material: COAL_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.7
      material: LAPIS_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.7
      material: REDSTONE_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.3
      material: COPPER_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 1.9
      material: IRON_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 1.7
      material: GOLD_BLOCK
  generator14:
    label: hero
    iconMaterial: GOLD_INGOT
    permission: oregen.hero
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.5
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.5
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.5
      material: LAPIS_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.5
      material: COPPER_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.5
      material: IRON_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.5
      material: GOLD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 11.0
      material: DIAMOND_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 14.0
      material: EMERALD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 8.5
      material: ANCIENT_DEBRIS
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.5
      material: AMETHYST_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.1
      material: COAL_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.1
      material: LAPIS_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.1
      material: REDSTONE_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.0
      material: COPPER_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: IRON_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.4
      material: GOLD_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.1
      material: DIAMOND_BLOCK
  generator15:
    label: legend
    iconMaterial: GOLD_INGOT
    permission: oregen.legend
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 4.6
      material: COAL_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 4.6
      material: REDSTONE_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 4.6
      material: LAPIS_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 4.6
      material: COPPER_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.0
      material: IRON_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.0
      material: GOLD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 9.0
      material: DIAMOND_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 11.0
      material: EMERALD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: ANCIENT_DEBRIS
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.8
      material: AMETHYST_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.7
      material: COAL_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.7
      material: LAPIS_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.7
      material: REDSTONE_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.2
      material: COPPER_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.2
      material: IRON_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.2
      material: GOLD_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: DIAMOND_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 1.7
      material: EMERALD_BLOCK
  generator16:
    label: superior
    iconMaterial: GOLD_INGOT
    permission: oregen.superior
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.0
      material: COPPER_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 4.0
      material: IRON_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 4.0
      material: GOLD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 8.0
      material: DIAMOND_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 9.0
      material: EMERALD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 7.0
      material: ANCIENT_DEBRIS
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.0
      material: AMETHYST_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.0
      material: COAL_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.0
      material: LAPIS_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.0
      material: REDSTONE_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.0
      material: COPPER_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: IRON_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: GOLD_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 4.1
      material: DIAMOND_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.1
      material: EMERALD_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.1
      material: NETHERITE_BLOCK
  generator17:
    label: superiorplus
    iconMaterial: GOLD_INGOT
    permission: oregen.superiorplus
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: false
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: STONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.5
      material: COBBLESTONE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 7.0
      material: DIAMOND_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 7.0
      material: EMERALD_ORE
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.0
      material: ANCIENT_DEBRIS
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: AMETHYST_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 6.5
      material: COAL_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 8.0
      material: LAPIS_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 8.0
      material: REDSTONE_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 8.0
      material: COPPER_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 7.0
      material: IRON_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 7.0
      material: GOLD_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: DIAMOND_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: EMERALD_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 3.0
      material: NETHERITE_BLOCK
  generator18:
    label: donator
    iconMaterial: IRON_BLOCK
    permission: oregen.donator
    unlock_islandLevel: 0
    conditions:
      cobblestone: true
      basalt: true
    blocks:
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 5.0
      material: NETHERITE_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 8.0
      material: DIAMOND_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 8.0
      material: EMERALD_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 7.0
      material: AMETHYST_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: QUARTZ_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: IRON_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: GOLD_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: LAPIS_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 10.0
      material: REDSTONE_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 11.0
      material: COPPER_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 9.0
      material: COAL_BLOCK
    - ==: xyz.spaceio.AdvancedOreGen.GeneratorItem
      chance: 2.0
      material: STONE
