package shyrcs.shopkeeper.gui;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.enums.TradeMode;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

import java.util.Arrays;
import java.util.List;

/**
 * Manages trade editing based on selected trade mode
 */
public class TradeEditManager {
    
    private final SoulShopkeeperPlugin plugin;
    
    public TradeEditManager(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Opens appropriate trade edit interface based on shopkeeper's trade mode
     */
    public void openTradeEdit(Player player, SoulShopkeeper shopkeeper, int tradeIndex) {
        TradeMode mode = shopkeeper.getTradeMode();
        
        switch (mode) {
            case GUI:
                openGUITradeEdit(player, shopkeeper, tradeIndex);
                break;
            case NATIVE:
            default:
                openNativeTradeEdit(player, shopkeeper, tradeIndex);
                break;
        }
    }
    
    /**
     * Opens GUI-based trade editing (crafting-style)
     */
    private void openGUITradeEdit(Player player, SoulShopkeeper shopkeeper, int tradeIndex) {
        // Ensure trade exists
        while (shopkeeper.getTrades().size() <= tradeIndex) {
            shopkeeper.addTrade(new ShopkeeperTrade("", "", "", -1, 0, true));
        }
        
        ShopkeeperTrade trade = shopkeeper.getTrades().get(tradeIndex);
        
        String title = MessageUtils.colorize("&6&lEdit Trade #" + (tradeIndex + 1) + " (GUI Mode)");
        Inventory gui = Bukkit.createInventory(null, 54, title);
        
        setupGUITradeEditLayout(gui, trade, tradeIndex);
        
        // Create session
        plugin.getGUIManager().createTradeEditSession(player, shopkeeper, tradeIndex);
        
        player.openInventory(gui);
    }
    
    /**
     * Opens native trade editing (villager-style)
     */
    private void openNativeTradeEdit(Player player, SoulShopkeeper shopkeeper, int tradeIndex) {
        // Use existing TradeEditGUI logic
        plugin.getGUIManager().openTradeEditGUI(player, shopkeeper, tradeIndex);
    }
    
    /**
     * Sets up GUI trade edit layout (crafting-style)
     */
    private void setupGUITradeEditLayout(Inventory gui, ShopkeeperTrade trade, int tradeIndex) {
        // Fill with gray glass
        ItemStack grayGlass = createGlassPane(Material.GRAY_STAINED_GLASS_PANE, " ");
        for (int i = 0; i < 54; i++) {
            gui.setItem(i, grayGlass);
        }
        
        // White glass for crafting area
        ItemStack whiteGlass = createGlassPane(Material.WHITE_STAINED_GLASS_PANE, " ");
        
        // Crafting grid area (3x3)
        int[] craftingSlots = {19, 20, 21, 28, 29, 30, 37, 38, 39};
        for (int slot : craftingSlots) {
            gui.setItem(slot, whiteGlass);
        }
        
        // Result slot
        gui.setItem(24, whiteGlass);
        
        // Place current trade items
        if (trade.getIngredient1Item() != null) {
            gui.setItem(19, trade.getIngredient1Item().clone()); // Top-left
        }
        if (trade.getIngredient2Item() != null) {
            gui.setItem(20, trade.getIngredient2Item().clone()); // Top-middle
        }
        if (trade.getResultItem() != null) {
            gui.setItem(24, trade.getResultItem().clone()); // Result
        }
        
        // Control buttons
        gui.setItem(45, createItem(Material.GREEN_CONCRETE, "&a&lSave Trade", 
                                  "&7Save current trade configuration"));
        
        gui.setItem(46, createItem(Material.RED_CONCRETE, "&c&lClear Trade", 
                                  "&7Clear all items from this trade"));
        
        gui.setItem(47, createItem(trade.isEnabled() ? Material.LIME_CONCRETE : Material.GRAY_CONCRETE,
                                  trade.isEnabled() ? "&a&lEnabled" : "&c&lDisabled",
                                  "&7Click to " + (trade.isEnabled() ? "disable" : "enable") + " this trade"));
        
        gui.setItem(49, createItem(Material.CRAFTING_TABLE, "&e&lSwitch to Native Mode",
                                  "&7Switch to villager-style editing",
                                  "&7Current: &eGUI Mode"));
        
        gui.setItem(53, createItem(Material.BARRIER, "&c&lBack", 
                                  "&7Return to shopkeeper menu"));
        
        // Info item
        gui.setItem(4, createItem(Material.BOOK, "&6&lGUI Trade Editor",
                                 "&7Place ingredients in crafting grid",
                                 "&7Place result in center slot",
                                 "&7Click save when finished"));
    }
    
    /**
     * Creates a glass pane item
     */
    private ItemStack createGlassPane(Material material, String name) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(MessageUtils.colorize(name));
            item.setItemMeta(meta);
        }
        return item;
    }
    
    /**
     * Creates an item with display name and lore
     */
    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(MessageUtils.colorize(name));
            if (lore.length > 0) {
                meta.setLore(Arrays.asList(lore).stream()
                    .map(MessageUtils::colorize)
                    .collect(java.util.stream.Collectors.toList()));
            }
            item.setItemMeta(meta);
        }
        return item;
    }
    
    /**
     * Handles saving trade from GUI mode
     */
    public void saveGUITrade(Player player, SoulShopkeeper shopkeeper, int tradeIndex, Inventory gui) {
        if (tradeIndex < 0 || tradeIndex >= shopkeeper.getTrades().size()) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cInvalid trade index!");
            return;
        }
        
        ShopkeeperTrade trade = shopkeeper.getTrades().get(tradeIndex);
        
        // Get items from GUI
        ItemStack ingredient1 = gui.getItem(19); // Top-left
        ItemStack ingredient2 = gui.getItem(20); // Top-middle  
        ItemStack result = gui.getItem(24);      // Result
        
        // Validate trade
        if (result == null || result.getType().isAir()) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cResult item is required!");
            return;
        }
        
        if (ingredient1 == null || ingredient1.getType().isAir()) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cAt least one ingredient is required!");
            return;
        }
        
        // Save trade
        try {
            trade.setIngredient1Item(ingredient1);
            trade.setIngredient2Item(ingredient2);
            trade.setResultItem(result);
            
            // Save to database
            saveShopkeeperToDatabase(shopkeeper);
            
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&aTrade #" + (tradeIndex + 1) + " saved successfully!");
            
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to save GUI trade: " + e.getMessage());
            e.printStackTrace();
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cFailed to save trade! Check console for errors.");
        }
    }
    
    /**
     * Saves shopkeeper to database
     */
    private void saveShopkeeperToDatabase(SoulShopkeeper shopkeeper) {
        try {
            if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                    (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
                wrapper.saveAll(); // Also save to YAML backup
            } else {
                plugin.getDataManager().saveAll();
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to save shopkeeper: " + e.getMessage());
            throw e;
        }
    }
}
