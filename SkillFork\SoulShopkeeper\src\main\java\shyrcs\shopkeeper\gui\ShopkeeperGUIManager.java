package shyrcs.shopkeeper.gui;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.enums.TradeMode;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

import java.util.*;

/**
 * Manages GUI interfaces for shopkeeper creation and editing
 */
public class ShopkeeperGUIManager {

    private final SoulShopkeeperPlugin plugin;
    private final TradeViewGUI tradeViewGUI;
    private final TradeEditManager tradeEditManager;

    // GUI sessions to track what players are doing
    private final Map<UUID, GUISession> activeSessions;
    
    public ShopkeeperGUIManager(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
        this.tradeViewGUI = new TradeViewGUI(plugin);
        this.tradeEditManager = new TradeEditManager(plugin);
        this.activeSessions = new HashMap<>();
    }
    
    /**
     * Opens the shopkeeper creation GUI
     */
    public void openCreationGUI(Player player) {
        String title = MessageUtils.colorize("&6&lCreate Shopkeeper");
        int size = 54;

        Inventory gui = Bukkit.createInventory(player, size, title);

        // Setup creation GUI layout
        setupCreationGUI(gui);

        // Create session
        GUISession session = new GUISession(player.getUniqueId(), GUIType.CREATION, null);
        activeSessions.put(player.getUniqueId(), session);

        player.openInventory(gui);
    }
    
    /**
     * Opens the shopkeeper editing GUI
     */
    public void openEditGUI(Player player, SoulShopkeeper shopkeeper) {
        String title = MessageUtils.colorize("&6&lEdit: " + shopkeeper.getName());
        int size = 54;

        Inventory gui = Bukkit.createInventory(player, size, title);

        // Setup edit GUI layout
        setupEditGUI(gui, shopkeeper);

        // Create session
        GUISession session = new GUISession(player.getUniqueId(), GUIType.EDIT, shopkeeper);
        activeSessions.put(player.getUniqueId(), session);

        player.openInventory(gui);
    }
    
    /**
     * Opens the trade editing GUI
     */
    public void openTradeEditGUI(Player player, SoulShopkeeper shopkeeper, int tradeIndex) {
        String title = MessageUtils.colorize("&6&lTrade Editor #" + (tradeIndex + 1));
        Inventory gui = Bukkit.createInventory(null, 27, title);

        // Setup trade edit GUI
        setupTradeEditGUI(gui, shopkeeper, tradeIndex);

        // Check if there's an existing session
        GUISession existingSession = activeSessions.get(player.getUniqueId());
        if (existingSession != null) {
            // Overwrite existing session
        }

        // Create session
        GUISession session = new GUISession(player.getUniqueId(), GUIType.TRADE_EDIT, shopkeeper);
        session.setTradeIndex(tradeIndex);
        activeSessions.put(player.getUniqueId(), session);



        // Open GUI with slight delay to ensure session is properly set
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            player.openInventory(gui);
        }, 1L);
    }
    
    /**
     * Sets up the creation GUI layout
     */
    private void setupCreationGUI(Inventory gui) {
        // Fill borders with glass panes
        fillBorders(gui, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
        
        // Add creation options
        gui.setItem(13, createItem(Material.VILLAGER_SPAWN_EGG, "&6Create Villager Shopkeeper", 
                                  "&7Click to create a villager shopkeeper"));
        
        gui.setItem(21, createItem(Material.ZOMBIE_SPAWN_EGG, "&6Create Zombie Shopkeeper", 
                                  "&7Click to create a zombie shopkeeper"));
        
        gui.setItem(23, createItem(Material.SKELETON_SPAWN_EGG, "&6Create Skeleton Shopkeeper", 
                                  "&7Click to create a skeleton shopkeeper"));
        
        // Add cancel button
        gui.setItem(49, createItem(Material.BARRIER, "&cCancel", "&7Click to cancel creation"));
    }
    
    /**
     * Sets up the edit GUI layout
     */
    private void setupEditGUI(Inventory gui, SoulShopkeeper shopkeeper) {
        // Fill borders with glass panes
        fillBorders(gui, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));

        // Shopkeeper info
        gui.setItem(4, createItem(Material.NAME_TAG, "&6Shopkeeper: " + shopkeeper.getName(),
                                 "&7Type: " + shopkeeper.getType(),
                                 "&7Trades: " + shopkeeper.getTradeCount(),
                                 "&7Active: " + (shopkeeper.isActive() ? "&aYes" : "&cNo")));

        // Setup trade slots based on trade mode
        if (shopkeeper.getTradeMode() == TradeMode.GUI) {
            setupGUIEditLayout(gui, shopkeeper);
        } else {
            setupNativeEditLayout(gui, shopkeeper);
        }

        // Control buttons
        gui.setItem(45, createItem(Material.EMERALD, "&aToggle Active",
                                  "&7Current: " + (shopkeeper.isActive() ? "&aActive" : "&cInactive"),
                                  "&7Click to toggle"));

        gui.setItem(46, createItem(Material.WRITABLE_BOOK, "&eEdit Name",
                                  "&7Current: &f" + shopkeeper.getName(),
                                  "&7Click to change shopkeeper name"));

        gui.setItem(47, createItem(Material.COMPASS, "&6&lTrade Mode: " + shopkeeper.getTradeMode().getDisplayName(),
                                  "&7Current mode: &e" + shopkeeper.getTradeMode().getDisplayName(),
                                  "&7" + shopkeeper.getTradeMode().getDescription(),
                                  "",
                                  "&7Available modes:",
                                  "&e• GUI Trading &7- Custom crafting interface",
                                  "&e• Native Trading &7- Minecraft villager style",
                                  "",
                                  "&eClick to change trade mode"));

        gui.setItem(49, createItem(Material.BARRIER, "&cClose", "&7Click to close editor"));

        gui.setItem(53, createItem(Material.REDSTONE, "&cDelete Shopkeeper",
                                  "&7Shift-click to delete this shopkeeper",
                                  "&c&lWARNING: This cannot be undone!"));
    }

    /**
     * Sets up GUI mode edit layout (crafting-style)
     */
    private void setupGUIEditLayout(Inventory gui, SoulShopkeeper shopkeeper) {
        List<ShopkeeperTrade> trades = shopkeeper.getTrades();

        // Crafting grid slots for ingredients: 10,11,12,19,20,21,28,29,30
        int[] craftingSlots = {10, 11, 12, 19, 20, 21, 28, 29, 30};
        // Result slot: 24
        int resultSlot = 24;
        // Golden glass slots: 15, 23, 25, 33
        int[] goldenGlassSlots = {15, 23, 25, 33};

        // Fill golden glass slots
        for (int slot : goldenGlassSlots) {
            gui.setItem(slot, createItem(Material.YELLOW_STAINED_GLASS_PANE, "&eDecoration"));
        }

        // Show trades in crafting-style layout
        int maxTrades = Math.min(trades.size() + (shopkeeper.canAddMoreTrades() ? 1 : 0), 1); // Only show first trade for now

        if (trades.size() > 0) {
            // Show first trade
            ShopkeeperTrade trade = trades.get(0);

            // Place ingredients in crafting grid
            if (trade.getIngredient1Item() != null) {
                gui.setItem(10, trade.getIngredient1Item().clone());
            }
            if (trade.getIngredient2Item() != null) {
                gui.setItem(11, trade.getIngredient2Item().clone());
            }

            // Place result
            if (trade.getResultItem() != null) {
                gui.setItem(24, trade.getResultItem().clone());
            }

            // Fill empty crafting slots with white glass
            for (int slot : craftingSlots) {
                if (gui.getItem(slot) == null) {
                    gui.setItem(slot, createItem(Material.WHITE_STAINED_GLASS_PANE, "&fCrafting Slot",
                                               "&7Place ingredients here"));
                }
            }

            // Result slot if empty
            if (gui.getItem(resultSlot) == null) {
                gui.setItem(resultSlot, createItem(Material.WHITE_STAINED_GLASS_PANE, "&fResult Slot",
                                                 "&7Place result item here"));
            }
        } else {
            // No trades - show empty crafting interface
            for (int slot : craftingSlots) {
                gui.setItem(slot, createItem(Material.WHITE_STAINED_GLASS_PANE, "&fCrafting Slot",
                                           "&7Place ingredients here"));
            }
            gui.setItem(resultSlot, createItem(Material.WHITE_STAINED_GLASS_PANE, "&fResult Slot",
                                             "&7Place result item here"));
        }
    }

    /**
     * Sets up Native mode edit layout (traditional list)
     */
    private void setupNativeEditLayout(Inventory gui, SoulShopkeeper shopkeeper) {
        // Trade slots - dynamic based on max trades setting
        int tradeStartSlot = 10;
        List<ShopkeeperTrade> trades = shopkeeper.getTrades();
        int maxTrades = shopkeeper.getMaxTrades();

        // Calculate how many slots to show (max 45 for GUI size limit)
        int slotsToShow = maxTrades == -1 ? 45 : Math.min(maxTrades, 45);

        for (int i = 0; i < slotsToShow; i++) {
            // Calculate slot position (skip border slots)
            int row = i / 7;
            int col = i % 7;
            int slot = tradeStartSlot + col + (row * 9);

            // Skip if slot is outside GUI bounds
            if (slot >= gui.getSize() - 9) break; // Leave space for control buttons

            if (i < trades.size()) {
                // Existing trade
                ShopkeeperTrade trade = trades.get(i);
                ItemStack tradeItem = createTradeDisplayItem(trade, i);
                gui.setItem(slot, tradeItem);
            } else if (shopkeeper.canAddMoreTrades()) {
                // Empty trade slot
                gui.setItem(slot, createItem(Material.LIME_STAINED_GLASS_PANE, "&aAdd Trade",
                                           "&7Click to add a new trade"));
            } else {
                // Disabled slot
                String maxText = maxTrades == -1 ? "Unlimited" : String.valueOf(maxTrades);
                gui.setItem(slot, createItem(Material.RED_STAINED_GLASS_PANE, "&cMax Trades Reached",
                                           "&7Maximum: " + maxText));
            }
        }
    }
    
    /**
     * Sets up the trade edit GUI layout
     */
    private void setupTradeEditGUI(Inventory gui, SoulShopkeeper shopkeeper, int tradeIndex) {
        // Clear GUI first
        gui.clear();

        // Fill borders with glass panes
        for (int i = 0; i < 9; i++) {
            gui.setItem(i, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
            gui.setItem(i + 18, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
        }
        gui.setItem(9, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));
        gui.setItem(17, createItem(Material.GRAY_STAINED_GLASS_PANE, " "));

        ShopkeeperTrade trade = null;
        if (tradeIndex >= 0 && tradeIndex < shopkeeper.getTrades().size()) {
            trade = shopkeeper.getTrades().get(tradeIndex);
        }

        // Trade setup slots - clear them first
        gui.setItem(10, null);
        gui.setItem(12, null);
        gui.setItem(16, null);

        // Load existing trade items if editing
        if (trade != null) {
            ItemStack ingredient1 = trade.getIngredient1Item();
            if (ingredient1 != null) {
                gui.setItem(10, ingredient1.clone());
            }

            ItemStack ingredient2 = trade.getIngredient2Item();
            if (ingredient2 != null) {
                gui.setItem(12, ingredient2.clone());
            }

            ItemStack result = trade.getResultItem();
            if (result != null) {
                gui.setItem(16, result.clone());
            }
        }

        // Add instruction items around the trade slots
        gui.setItem(9, createItem(Material.YELLOW_STAINED_GLASS_PANE, "&eIngredient 1",
                                 "&7Drag items to slot 10 →"));
        gui.setItem(11, createItem(Material.YELLOW_STAINED_GLASS_PANE, "&eIngredient 2",
                                  "&7Drag items to slot 12 →"));
        gui.setItem(15, createItem(Material.GREEN_STAINED_GLASS_PANE, "&aResult",
                                  "&7Drag items to slot 16 →"));

        // Control buttons
        gui.setItem(22, createItem(Material.EMERALD, "&aSave Trade", "&7Click to save this trade"));
        gui.setItem(24, createItem(Material.BARRIER, "&cBack", "&7Click to go back"));

        if (trade != null) {
            gui.setItem(26, createItem(Material.REDSTONE, "&cDelete Trade", "&7Click to delete this trade"));
        }
    }
    
    /**
     * Creates a display item for a trade
     */
    private ItemStack createTradeDisplayItem(ShopkeeperTrade trade, int index) {
        Material material = Material.PAPER;
        String name = "&6Trade #" + (index + 1);
        List<String> lore = new ArrayList<>();
        
        // Get trade items for display
        ItemStack result = trade.getResultItem();
        ItemStack ingredient1 = trade.getIngredient1Item();
        ItemStack ingredient2 = trade.getIngredient2Item();
        
        if (result != null) {
            lore.add("&7Result: &f" + getItemDisplayName(result));
        }
        
        if (ingredient1 != null) {
            lore.add("&7Ingredient 1: &f" + getItemDisplayName(ingredient1));
        }
        
        if (ingredient2 != null) {
            lore.add("&7Ingredient 2: &f" + getItemDisplayName(ingredient2));
        }
        
        lore.add("");
        lore.add("&7Status: " + (trade.isEnabled() ? "&aEnabled" : "&cDisabled"));
        
        if (trade.getMaxUses() != -1) {
            lore.add("&7Uses: &f" + trade.getUses() + "/" + trade.getMaxUses());
        } else {
            lore.add("&7Uses: &fUnlimited");
        }
        
        lore.add("");
        lore.add("&eLeft-click to edit");
        lore.add("&eRight-click to toggle enabled");
        
        return createItem(material, name, lore.toArray(new String[0]));
    }
    
    /**
     * Gets display name for an item
     */
    private String getItemDisplayName(ItemStack item) {
        if (item == null) return "None";
        
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return MessageUtils.stripColors(item.getItemMeta().getDisplayName());
        }
        
        // Check if it's an MMOItem
        String mmoInfo = plugin.getMMOItemStorage().getMMOItemInfo(item);
        if (mmoInfo != null) {
            return mmoInfo;
        }
        
        return item.getType().name().toLowerCase().replace('_', ' ');
    }
    
    /**
     * Creates an ItemStack with name and lore
     */
    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(MessageUtils.colorize(name));
            
            if (lore.length > 0) {
                List<String> coloredLore = new ArrayList<>();
                for (String line : lore) {
                    coloredLore.add(MessageUtils.colorize(line));
                }
                meta.setLore(coloredLore);
            }
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Fills the borders of a GUI with the specified item
     */
    private void fillBorders(Inventory gui, ItemStack item) {
        int size = gui.getSize();
        int rows = size / 9;
        
        // Top and bottom rows
        for (int i = 0; i < 9; i++) {
            gui.setItem(i, item);
            gui.setItem(size - 9 + i, item);
        }
        
        // Left and right columns
        for (int i = 1; i < rows - 1; i++) {
            gui.setItem(i * 9, item);
            gui.setItem(i * 9 + 8, item);
        }
    }
    
    // Session management
    public GUISession getSession(UUID playerId) {
        GUISession session = activeSessions.get(playerId);
        plugin.getLogger().info("Getting session for player: " + playerId +
                               ", found: " + (session != null ? session.getType() : "null") +
                               ", total sessions: " + activeSessions.size());
        return session;
    }
    
    public void removeSession(UUID playerId) {
        GUISession session = activeSessions.get(playerId);
        if (session != null) {
            plugin.getLogger().info("Attempting to remove session for player: " + playerId +
                                   ", type: " + session.getType());

            // Add stack trace to see who's calling this
            plugin.getLogger().info("Session removal called from:");
            StackTraceElement[] stack = Thread.currentThread().getStackTrace();
            for (int i = 2; i < Math.min(6, stack.length); i++) {
                plugin.getLogger().info("  " + stack[i].toString());
            }
        }

        GUISession removed = activeSessions.remove(playerId);
        plugin.getLogger().info("Removed session for player: " + playerId +
                               ", was: " + (removed != null ? removed.getType() : "null") +
                               ", remaining sessions: " + activeSessions.size());
    }

    public void removeSessionSafely(UUID playerId, GUIType expectedType) {
        GUISession session = activeSessions.get(playerId);
        if (session != null && session.getType() == expectedType) {
            activeSessions.remove(playerId);
            plugin.getLogger().info("Safely removed " + expectedType + " session for player: " + playerId);
        } else {
            plugin.getLogger().info("Skipped removal - session type mismatch for player: " + playerId +
                                   ", expected: " + expectedType + ", actual: " +
                                   (session != null ? session.getType() : "null"));
        }
    }
    
    public boolean hasSession(UUID playerId) {
        return activeSessions.containsKey(playerId);
    }

    /**
     * Creates a trade view session
     */
    public void createTradeViewSession(Player player, SoulShopkeeper shopkeeper, int tradeIndex) {
        GUISession session = new GUISession(player.getUniqueId(), GUIType.TRADE_VIEW, shopkeeper);
        session.setTradeIndex(tradeIndex);
        activeSessions.put(player.getUniqueId(), session);
    }

    /**
     * Opens the trade view GUI
     */
    public void openTradeViewGUI(Player player, SoulShopkeeper shopkeeper, int tradeIndex) {
        tradeViewGUI.openTradeViewGUI(player, shopkeeper, tradeIndex);
    }

    /**
     * Creates a trade view mode selection session
     */
    public void createTradeViewModeSession(Player player, SoulShopkeeper shopkeeper) {
        GUISession session = new GUISession(player.getUniqueId(), GUIType.TRADE_VIEW_MODE, shopkeeper);
        activeSessions.put(player.getUniqueId(), session);
    }

    /**
     * Creates a trade list selection session
     */
    public void createTradeListSession(Player player, SoulShopkeeper shopkeeper) {
        GUISession session = new GUISession(player.getUniqueId(), GUIType.TRADE_LIST, shopkeeper);
        activeSessions.put(player.getUniqueId(), session);
    }

    /**
     * Opens trade edit based on shopkeeper's trade mode
     */
    public void openTradeEditByMode(Player player, SoulShopkeeper shopkeeper, int tradeIndex) {
        tradeEditManager.openTradeEdit(player, shopkeeper, tradeIndex);
    }

    /**
     * Opens trade mode selection GUI
     */
    public void openTradeModeSelection(Player player, SoulShopkeeper shopkeeper) {
        String title = MessageUtils.colorize("&6&lSelect Trade Mode");
        Inventory gui = Bukkit.createInventory(null, 27, title);

        // Fill with glass
        ItemStack glass = createItem(Material.GRAY_STAINED_GLASS_PANE, " ");
        for (int i = 0; i < 27; i++) {
            gui.setItem(i, glass);
        }

        // GUI Mode option
        gui.setItem(11, createItem(Material.CRAFTING_TABLE,
                                  "&e&lGUI Trading Mode",
                                  "&7Custom crafting-style interface",
                                  "&7• Visual crafting grid layout",
                                  "&7• Easy to understand recipes",
                                  "&7• Better for complex trades",
                                  "",
                                  shopkeeper.getTradeMode() == TradeMode.GUI ? "&a&lCurrently Selected" : "&eClick to select"));

        // Native Mode option
        gui.setItem(15, createItem(Material.EMERALD,
                                  "&a&lNative Trading Mode",
                                  "&7Standard Minecraft villager interface",
                                  "&7• Familiar to all players",
                                  "&7• Quick and simple setup",
                                  "&7• Compatible with all plugins",
                                  "",
                                  shopkeeper.getTradeMode() == TradeMode.NATIVE ? "&a&lCurrently Selected" : "&eClick to select"));

        // Back button
        gui.setItem(22, createItem(Material.BARRIER, "&c&lBack", "&7Return to shopkeeper menu"));

        // Remove existing session and create new one
        removeSession(player.getUniqueId());
        createTradeModeSession(player, shopkeeper);

        player.openInventory(gui);
    }

    /**
     * Creates a trade mode selection session
     */
    public void createTradeModeSession(Player player, SoulShopkeeper shopkeeper) {
        plugin.getLogger().info("Creating TRADE_MODE session for player: " + player.getName());
        GUISession session = new GUISession(player.getUniqueId(), GUIType.TRADE_MODE, shopkeeper);
        activeSessions.put(player.getUniqueId(), session);
        plugin.getLogger().info("TRADE_MODE session created for player: " + player.getName() + ", total sessions: " + activeSessions.size());
    }

    /**
     * Creates a trade edit session
     */
    public void createTradeEditSession(Player player, SoulShopkeeper shopkeeper, int tradeIndex) {
        GUISession session = new GUISession(player.getUniqueId(), GUIType.TRADE_EDIT, shopkeeper);
        session.setTradeIndex(tradeIndex);
        activeSessions.put(player.getUniqueId(), session);
    }
    
    // GUI Session class
    public static class GUISession {
        private final UUID playerId;
        private final GUIType type;
        private final SoulShopkeeper shopkeeper;
        private int tradeIndex = -1;
        
        public GUISession(UUID playerId, GUIType type, SoulShopkeeper shopkeeper) {
            this.playerId = playerId;
            this.type = type;
            this.shopkeeper = shopkeeper;
        }
        
        public UUID getPlayerId() { return playerId; }
        public GUIType getType() { return type; }
        public SoulShopkeeper getShopkeeper() { return shopkeeper; }
        public int getTradeIndex() { return tradeIndex; }
        public void setTradeIndex(int tradeIndex) { this.tradeIndex = tradeIndex; }
    }
    
    // GUI Types
    public enum GUIType {
        CREATION,
        EDIT,
        TRADE_EDIT,
        TRADE_VIEW,
        TRADE_VIEW_MODE,
        TRADE_LIST,
        TRADE_MODE
    }
}
