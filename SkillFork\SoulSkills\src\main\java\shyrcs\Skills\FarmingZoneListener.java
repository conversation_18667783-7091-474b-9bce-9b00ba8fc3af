package shyrcs.Skills;

import io.lumine.mythic.lib.api.item.NBTItem;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import shyrcs.Ability.CooldownManager;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Listener xử lý Farming Zone skill activation thông qua NBT tags
 * Format NBT: farming_zone <range> <time> <click_type> <cooldown>
 * Ví dụ: farming_zone 8 30 right_click 60
 */
public class FarmingZoneListener implements Listener {
    
    private final Plugin plugin;
    private final FarmingZoneEffect farmingZoneEffect;
    private final Map<UUID, Long> cooldowns = new HashMap<>();
    
    public FarmingZoneListener(Plugin plugin) {
        this.plugin = plugin;
        this.farmingZoneEffect = new FarmingZoneEffect(plugin);
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();
        
        if (item == null || !item.hasItemMeta()) {
            return;
        }
        
        // Đọc NBT tags để tìm farming_zone skill
        NBTItem nbtItem = NBTItem.get(item);
        String farmingZoneConfig = findFarmingZoneConfig(nbtItem);
        
        if (farmingZoneConfig == null) {
            return; // Không có farming_zone skill
        }
        
        // Parse config: farming_zone <range> <time> <click_type> <cooldown>
        String[] parts = farmingZoneConfig.split(" ");
        if (parts.length != 5 || !parts[0].equals("farming_zone")) {
            player.sendMessage("§c[Farming Zone] §7Cấu hình NBT không hợp lệ! Format: farming_zone <range> <time> <click_type> <cooldown>");
            return;
        }

        double range;
        int duration;
        String requiredClickType = parts[3].toLowerCase();
        int cooldownSeconds;

        try {
            range = Double.parseDouble(parts[1]);
            duration = Integer.parseInt(parts[2]);
            cooldownSeconds = Integer.parseInt(parts[4]);
        } catch (NumberFormatException e) {
            player.sendMessage("§c[Farming Zone] §7Tham số không hợp lệ! Range, time và cooldown phải là số!");
            return;
        }

        // Validate parameters
        if (range <= 0) {
            player.sendMessage("§c[Farming Zone] §7Range phải > 0!");
            return;
        }
        if (range > 50) {
            player.sendMessage("§c[Farming Zone] §7Range không nên vượt quá 50 blocks!");
            return;
        }
        if (duration <= 0) {
            player.sendMessage("§c[Farming Zone] §7Duration phải > 0!");
            return;
        }
        if (cooldownSeconds < 0) {
            player.sendMessage("§c[Farming Zone] §7Cooldown phải >= 0!");
            return;
        }
        
        // Kiểm tra click type
        String actualClickType = getClickType(event.getAction(), player.isSneaking());
        if (!actualClickType.equals(requiredClickType)) {
            return; // Không phải click type đúng
        }
        
        // Cancel event để tránh các hành động khác
        event.setCancelled(true);
        
        // Kiểm tra cooldown
        UUID playerId = player.getUniqueId();
        long currentTime = System.currentTimeMillis();
        
        if (cooldowns.containsKey(playerId)) {
            long lastUse = cooldowns.get(playerId);
            long timePassed = (currentTime - lastUse) / 1000;
            
            if (timePassed < cooldownSeconds) {
                long remaining = cooldownSeconds - timePassed;
                
                // Sử dụng CooldownManager để hiển thị cooldown trên ActionBar
                CooldownManager cooldownManager = CooldownManager.getInstance();
                if (cooldownManager != null) {
                    cooldownManager.setCooldown(player, "Farming Zone", (int) remaining);
                }
                
                player.sendMessage("§c[Farming Zone] §7Còn §e" + remaining + "s §7cooldown!");
                return;
            }
        }
        
        // Kiểm tra nếu player đã có farming zone active
        if (farmingZoneEffect.hasActiveFarmingZone(playerId)) {
            player.sendMessage("§c[Farming Zone] §7Bạn đã có Farming Zone đang hoạt động!");
            return;
        }
        
        // Kích hoạt Farming Zone với custom range
        farmingZoneEffect.activateFarmingZone(player, range, duration);

        // Set cooldown
        cooldowns.put(playerId, currentTime);
        CooldownManager cooldownManager = CooldownManager.getInstance();
        if (cooldownManager != null) {
            cooldownManager.setCooldown(player, "Farming Zone", cooldownSeconds);
        }

        // Thông báo thành công
        player.sendMessage("§a[Farming Zone] §7Đã kích hoạt Farming Zone! Range: §e" + range + " blocks §7Duration: §e" + duration + "s §7Cooldown: §e" + cooldownSeconds + "s");
    }
    
    /**
     * Tìm farming_zone config trong NBT tags của item
     * Format mới: soulskills = "farming_zone <range> <time> <click> <cooldown>"
     */
    private String findFarmingZoneConfig(NBTItem nbtItem) {
        // Kiểm tra tag "soulskills" trước (format mới)
        if (nbtItem.hasTag("soulskills")) {
            String value = nbtItem.getString("soulskills");
            if (value != null && value.startsWith("farming_zone")) {
                return value;
            }
        }

        // Fallback: kiểm tra các tag cũ để backward compatibility
        String[] possibleTags = {
            "MMOITEMS_FARMING_ZONE",
            "farming_zone", 
            "FARMING_ZONE",
            "skill_farming_zone",
            "SKILL_FARMING_ZONE"
        };
        
        for (String tag : possibleTags) {
            if (nbtItem.hasTag(tag)) {
                String value = nbtItem.getString(tag);
                if (value != null && value.startsWith("farming_zone")) {
                    return value;
                }
            }
        }
        
        // Fallback search
        for (String key : nbtItem.getTags()) {
            if (key.toLowerCase().contains("farming") || key.toLowerCase().contains("zone")) {
                String value = nbtItem.getString(key);
                if (value != null && value.startsWith("farming_zone")) {
                    return value;
                }
            }
        }
        
        return null;
    }
    
    /**
     * Xác định loại click từ event
     */
    private String getClickType(Action action, boolean isSneaking) {
        String baseType;
        
        switch (action) {
            case LEFT_CLICK_AIR:
            case LEFT_CLICK_BLOCK:
                baseType = "left_click";
                break;
            case RIGHT_CLICK_AIR:
            case RIGHT_CLICK_BLOCK:
                baseType = "right_click";
                break;
            default:
                return "unknown";
        }
        
        if (isSneaking) {
            return "shift_" + baseType;
        } else {
            return baseType;
        }
    }
    
    /**
     * Cleanup khi plugin disable
     */
    public void cleanup() {
        farmingZoneEffect.cleanup();
        cooldowns.clear();
    }
    
    /**
     * Lấy FarmingZoneEffect instance
     */
    public FarmingZoneEffect getFarmingZoneEffect() {
        return farmingZoneEffect;
    }
}
