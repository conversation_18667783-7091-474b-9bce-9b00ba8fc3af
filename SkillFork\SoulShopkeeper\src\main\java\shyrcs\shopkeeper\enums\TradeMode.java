package shyrcs.shopkeeper.enums;

/**
 * Enum for different trade modes
 */
public enum TradeMode {
    /**
     * Native Minecraft villager trading interface
     */
    NATIVE("Native Trading", "Standard Minecraft villager interface"),
    
    /**
     * Custom GUI-based trading with crafting grid layout
     */
    GUI("GUI Trading", "Custom crafting-style interface");
    
    private final String displayName;
    private final String description;
    
    TradeMode(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Get trade mode from string (case insensitive)
     */
    public static TradeMode fromString(String str) {
        if (str == null) return NATIVE; // Default
        
        switch (str.toUpperCase()) {
            case "GUI":
            case "CUSTOM":
            case "CRAFTING":
                return GUI;
            case "NATIVE":
            case "VILLAGER":
            case "VANILLA":
            default:
                return NATIVE;
        }
    }
}
