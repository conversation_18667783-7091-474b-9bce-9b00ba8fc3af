package shyrcs.Skills;

import org.bukkit.*;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.block.data.Ageable;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;

/**
 * Class xử lý logic Farming Zone effect
 */
public class FarmingZoneEffect {

    private static final int CROPS_PER_SECOND = 5; // 5 cây/giây
    private static final int TICKS_PER_CROP = 20 / CROPS_PER_SECOND; // 4 ticks per crop
    
    private final Plugin plugin;
    private final Map<UUID, FarmingZoneInstance> activeFarmingZones = new HashMap<>();
    
    // Supported crops
    private static final Set<Material> FARMABLE_CROPS = Set.of(
        Material.WHEAT, Material.CARROTS, Material.POTATOES, Material.BEETROOTS,
        Material.NETHER_WART, Material.COCOA, Material.SWEET_BERRY_BUSH
    );
    
    // Crop to replant block mapping (block materials only)
    private static final Map<Material, Material> CROP_TO_REPLANT_BLOCK = Map.of(
        Material.WHEAT, Material.WHEAT,
        Material.CARROTS, Material.CARROTS,
        Material.POTATOES, Material.POTATOES,
        Material.BEETROOTS, Material.BEETROOTS,
        Material.NETHER_WART, Material.NETHER_WART,
        Material.SWEET_BERRY_BUSH, Material.SWEET_BERRY_BUSH
    );
    
    public FarmingZoneEffect(Plugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Kích hoạt Farming Zone với custom range
     */
    public void activateFarmingZone(Player player, double farmingRadius, int durationSeconds) {
        UUID playerId = player.getUniqueId();

        // Kiểm tra bảo vệ trước khi kích hoạt
        if (!canUseFarmingZone(player, player.getLocation())) {
            player.sendMessage("§c[Farming Zone] §7Không thể sử dụng Farming Zone tại khu vực được bảo vệ!");
            return;
        }

        // Tạo hologram
        FarmingZoneHologram hologram = new FarmingZoneHologram(plugin, player);
        
        // Tạo farming zone instance với custom range
        FarmingZoneInstance instance = new FarmingZoneInstance(player, hologram, farmingRadius, durationSeconds);
        activeFarmingZones.put(playerId, instance);

        // Bắt đầu farming task
        startFarmingTask(instance);
        
        player.sendMessage("§a[Farming Zone] §7Hologram đã được triệu hồi tại vị trí cố định! Bắt đầu auto farming...");
    }
    
    /**
     * Bắt đầu farming task
     */
    private void startFarmingTask(FarmingZoneInstance instance) {
        BukkitTask task = new BukkitRunnable() {
            private int ticks = 0;
            private final int maxTicks = instance.getDuration() * 20;
            private int cropTicks = 0;
            
            @Override
            public void run() {
                Player player = instance.getPlayer();
                FarmingZoneHologram hologram = instance.getHologram();
                
                // Kiểm tra validity
                if (ticks >= maxTicks || !player.isOnline() || !hologram.isValid()) {
                    endFarmingZone(player.getUniqueId());
                    this.cancel();
                    return;
                }
                
                // Farm crops mỗi TICKS_PER_CROP ticks
                if (cropTicks >= TICKS_PER_CROP) {
                    farmNextCrop(hologram, instance.getFarmingRadius());
                    cropTicks = 0;
                } else {
                    cropTicks++;
                }
                
                ticks++;
            }
        }.runTaskTimer(plugin, 0L, 1L);
        
        instance.setTask(task);
    }
    
    /**
     * Farm một cây tiếp theo với custom range
     */
    private void farmNextCrop(FarmingZoneHologram hologram, double farmingRadius) {
        Location hologramLoc = hologram.getLocation();
        if (hologramLoc == null) return;
        
        Player player = hologram.getOwner();
        
        // Tìm cây trồng trong bán kính custom
        List<Block> farmableCrops = findFarmableCrops(hologramLoc, farmingRadius);

        if (farmableCrops.isEmpty()) {
            return; // Không có cây nào để farm
        }
        
        // Chọn cây ngẫu nhiên
        Block targetCrop = farmableCrops.get(new Random().nextInt(farmableCrops.size()));

        // Harvesting crop

        // Tạo tia laser từ hologram đến cây
        createLaserBeam(hologramLoc, targetCrop.getLocation().add(0.5, 0.5, 0.5));

        // Harvest và replant
        harvestAndReplant(targetCrop, player);
    }
    
    /**
     * Tìm các cây trồng có thể farm trong bán kính custom
     */
    private List<Block> findFarmableCrops(Location center, double farmingRadius) {
        List<Block> crops = new ArrayList<>();
        World world = center.getWorld();

        int centerX = center.getBlockX();
        int centerY = center.getBlockY();
        int centerZ = center.getBlockZ();

        // Scan area for crops
        
        // Scan trong bán kính custom
        for (int x = centerX - (int) farmingRadius; x <= centerX + farmingRadius; x++) {
            for (int y = centerY - 10; y <= centerY + 5; y++) { // Scan từ 10 blocks dưới đến 5 blocks trên
                for (int z = centerZ - (int) farmingRadius; z <= centerZ + farmingRadius; z++) {

                    // Kiểm tra distance
                    double distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(z - centerZ, 2));
                    if (distance > farmingRadius) continue;
                    
                    Block block = world.getBlockAt(x, y, z);

                    // Check if block is farmable crop

                    // Kiểm tra nếu là cây trồng mature và không được bảo vệ
                    if (isMatureCrop(block) && canFarmAtLocation(block.getLocation())) {
                        crops.add(block);
                    }
                }
            }
        }
        
        return crops;
    }
    
    /**
     * Kiểm tra block có phải là cây trồng mature không
     */
    private boolean isMatureCrop(Block block) {
        Material type = block.getType();
        
        if (!FARMABLE_CROPS.contains(type)) {
            return false;
        }
        
        // Kiểm tra age cho crops có Ageable data
        if (block.getBlockData() instanceof Ageable) {
            Ageable ageable = (Ageable) block.getBlockData();
            return ageable.getAge() == ageable.getMaximumAge();
        }
        
        // Đối với cocoa và một số crops khác
        return true;
    }
    
    /**
     * Tạo tia laser từ hologram đến target
     */
    private void createLaserBeam(Location from, Location to) {
        // Tạo particle line từ hologram đến cây
        double distance = from.distance(to);
        int particles = (int) (distance * 2); // 2 particles per block
        
        for (int i = 0; i <= particles; i++) {
            double ratio = (double) i / particles;
            double x = from.getX() + (to.getX() - from.getX()) * ratio;
            double y = from.getY() + (to.getY() - from.getY()) * ratio;
            double z = from.getZ() + (to.getZ() - from.getZ()) * ratio;
            
            Location particleLoc = new Location(from.getWorld(), x, y, z);
            
            // Tạo green laser effect
            from.getWorld().spawnParticle(Particle.HAPPY_VILLAGER, particleLoc, 1, 0, 0, 0, 0);
        }
        
        // Tạo impact effect tại target
        to.getWorld().spawnParticle(Particle.HEART, to, 3, 0.2, 0.2, 0.2, 0);
    }
    
    /**
     * Harvest và replant cây trồng
     */
    private void harvestAndReplant(Block cropBlock, Player player) {
        Material cropType = cropBlock.getType();

        // Lấy drops
        Collection<ItemStack> drops = cropBlock.getDrops();

        // Break block
        cropBlock.setType(Material.AIR);

        // Track storage integration
        boolean anyAddedToStorage = false;
        int totalItemsHarvested = 0;

        // Thử thêm vào ExtraStorage trước, nếu không được thì drop
        for (ItemStack drop : drops) {
            totalItemsHarvested += drop.getAmount();
            boolean addedToStorage = tryAddToExtraStorage(player, drop);

            if (addedToStorage) {
                anyAddedToStorage = true;
            } else {
                // Nếu không thể thêm vào storage, drop tại vị trí player
                player.getWorld().dropItemNaturally(player.getLocation(), drop);
            }
        }

        // Fire custom event with error handling
        try {
            FarmingZoneEvent farmingEvent = new FarmingZoneEvent(
                player, cropBlock, drops, anyAddedToStorage,
                totalItemsHarvested, cropType.name()
            );
            Bukkit.getPluginManager().callEvent(farmingEvent);
        } catch (Exception e) {
            plugin.getLogger().warning("Error firing FarmingZoneEvent: " + e.getMessage());
        }
        
        // Replant crop
        Material replantType = CROP_TO_REPLANT_BLOCK.get(cropType);
        if (replantType != null) {
            // Kiểm tra soil
            Block soilBlock = cropBlock.getRelative(BlockFace.DOWN);
            if (soilBlock.getType() == Material.FARMLAND ||
                soilBlock.getType() == Material.SOUL_SAND ||
                soilBlock.getType() == Material.SOUL_SOIL) {

                // Replant với age 0 (young crop)
                try {
                    cropBlock.setType(replantType);

                    // Set age to 0 for ageable crops
                    if (cropBlock.getBlockData() instanceof Ageable) {
                        Ageable ageable = (Ageable) cropBlock.getBlockData();
                        ageable.setAge(0);
                        cropBlock.setBlockData(ageable);
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("Error replanting crop: " + e.getMessage());
                }
            }
        }
        
        // Sound effect
        player.getWorld().playSound(cropBlock.getLocation(), Sound.BLOCK_CROP_BREAK, 0.5f, 1.0f);
    }
    
    /**
     * Kết thúc Farming Zone
     */
    private void endFarmingZone(UUID playerId) {
        FarmingZoneInstance instance = activeFarmingZones.remove(playerId);
        if (instance != null) {
            // Cancel task
            if (instance.getTask() != null) {
                instance.getTask().cancel();
            }
            
            // Remove hologram
            instance.getHologram().remove();
            
            // Notify player
            Player player = instance.getPlayer();
            if (player != null && player.isOnline()) {
                player.sendMessage("§a[Farming Zone] §7Farming Zone đã kết thúc!");
            }
        }
    }
    
    /**
     * Kiểm tra player có farming zone active không
     */
    public boolean hasActiveFarmingZone(UUID playerId) {
        return activeFarmingZones.containsKey(playerId);
    }
    
    /**
     * Thử thêm item vào ExtraStorage bằng reflection
     */
    private boolean tryAddToExtraStorage(Player player, ItemStack item) {
        try {
            // Kiểm tra xem ExtraStorage plugin có tồn tại không
            if (Bukkit.getPluginManager().getPlugin("ExtraStorage") == null) {
                return false;
            }

            // Lấy StorageAPI instance
            Class<?> storageAPIClass = Class.forName("me.hsgamer.extrastorage.api.StorageAPI");
            Object storageAPI = storageAPIClass.getMethod("getInstance").invoke(null);

            // Lấy User từ player UUID
            Class<?> userClass = Class.forName("me.hsgamer.extrastorage.data.user.User");
            Object user = storageAPIClass.getMethod("getUser", java.util.UUID.class)
                .invoke(storageAPI, player.getUniqueId());

            if (user == null) return false;

            // Lấy Storage từ User
            Object storage = userClass.getMethod("getStorage").invoke(user);
            if (storage == null) return false;

            // Kiểm tra storage status và canStore
            Class<?> storageClass = Class.forName("me.hsgamer.extrastorage.api.storage.Storage");
            java.lang.reflect.Method getStatusMethod = storageClass.getMethod("getStatus");
            java.lang.reflect.Method canStoreMethod = storageClass.getMethod("canStore", Object.class);
            java.lang.reflect.Method isMaxSpaceMethod = storageClass.getMethod("isMaxSpace");

            boolean status = (Boolean) getStatusMethod.invoke(storage);
            boolean canStore = (Boolean) canStoreMethod.invoke(storage, item);
            boolean isMaxSpace = (Boolean) isMaxSpaceMethod.invoke(storage);

            if (!status || isMaxSpace) return false;

            if (!canStore) {
                // Thử thêm item mới vào filter trước
                try {
                    java.lang.reflect.Method addNewItemMethod = storageClass.getMethod("addNewItem", Object.class);
                    addNewItemMethod.invoke(storage, item);

                    // Kiểm tra lại canStore sau khi thêm item mới
                    canStore = (Boolean) canStoreMethod.invoke(storage, item);
                    if (!canStore) return false;
                } catch (Exception e) {
                    return false;
                }
            }

            // Thêm vào storage
            java.lang.reflect.Method addMethod = storageClass.getMethod("add", Object.class, int.class);
            addMethod.invoke(storage, item, item.getAmount());

            // Thông báo cho player (optional - có thể comment out nếu spam)
            // player.sendMessage("§a[Farming Zone] §7Đã thêm §e" + item.getAmount() + "x " +
            //                   getItemDisplayName(item) + " §7vào storage!");

            return true;

        } catch (Exception e) {
            // Log error nếu cần debug
            // plugin.getLogger().warning("Error adding to ExtraStorage: " + e.getMessage());
            return false;
        }
    }

    /**
     * Kiểm tra xem player có thể sử dụng Farming Zone tại location không
     */
    private boolean canUseFarmingZone(Player player, Location location) {
        // Tạm thời disable WorldGuard để test SuperiorSkyblock
        // if (!canUseWorldGuard(player, location)) {
        //     return false;
        // }

        // Kiểm tra Superior Skyblock
        if (!canUseSuperiorSkyblock(player, location)) {
            return false;
        }

        return true;
    }

    /**
     * Kiểm tra xem có thể farm tại location cụ thể không
     */
    private boolean canFarmAtLocation(Location location) {
        // Kiểm tra WorldGuard cho location cụ thể
        if (!canBreakBlockWorldGuard(location)) {
            return false;
        }

        // Kiểm tra Superior Skyblock cho location cụ thể
        if (!canBreakBlockSuperiorSkyblock(location)) {
            return false;
        }

        return true;
    }

    /**
     * Kiểm tra WorldGuard permissions bằng cách test với fake event
     */
    private boolean canUseWorldGuard(Player player, Location location) {
        try {
            // Kiểm tra xem WorldGuard có tồn tại không
            if (Bukkit.getPluginManager().getPlugin("WorldGuard") == null) {
                return true; // Không có WorldGuard = cho phép
            }

            // Tạo một fake BlockBreakEvent để test permissions
            // Nhưng không call event thực sự, chỉ dùng để test
            org.bukkit.block.Block block = location.getBlock();

            // Kiểm tra bằng cách thử đặt block tạm thời
            org.bukkit.Material originalType = block.getType();

            // Nếu block đã là AIR thì có thể build
            if (originalType == org.bukkit.Material.AIR) {
                return true;
            }

            // Sử dụng reflection để check WorldGuard regions
            Class<?> worldGuardClass = Class.forName("com.sk89q.worldguard.WorldGuard");
            Object worldGuard = worldGuardClass.getMethod("getInstance").invoke(null);

            Object platform = worldGuardClass.getMethod("getPlatform").invoke(worldGuard);
            Object regionContainer = platform.getClass().getMethod("getRegionContainer").invoke(platform);

            // Convert Bukkit world to WorldEdit world
            Class<?> bukkitAdapterClass = Class.forName("com.sk89q.worldedit.bukkit.BukkitAdapter");
            Object worldEditWorld = bukkitAdapterClass.getMethod("adapt", org.bukkit.World.class).invoke(null, location.getWorld());

            Object regionManager = regionContainer.getClass().getMethod("get", Class.forName("com.sk89q.worldedit.world.World"))
                .invoke(regionContainer, worldEditWorld);

            if (regionManager == null) {
                return true; // Không có region manager = cho phép
            }

            // Convert location to WorldEdit location
            Object wgLocation = bukkitAdapterClass.getMethod("adapt", Location.class).invoke(null, location);

            // Get regions
            Object applicableRegions = regionManager.getClass().getMethod("getApplicableRegions",
                Class.forName("com.sk89q.worldedit.util.Location")).invoke(regionManager, wgLocation);

            // Nếu có regions thì kiểm tra member
            int regionCount = (Integer) applicableRegions.getClass().getMethod("size").invoke(applicableRegions);

            if (regionCount > 0) {
                // Có regions, kiểm tra xem player có phải member không
                Object wgPlayer = bukkitAdapterClass.getMethod("adapt", org.bukkit.entity.Player.class).invoke(null, player);

                // Test build flag
                try {
                    Class<?> flagsClass = Class.forName("com.sk89q.worldguard.protection.flags.Flags");
                    Object buildFlag = flagsClass.getField("BUILD").get(null);

                    Object result = applicableRegions.getClass().getMethod("testState",
                        Class.forName("com.sk89q.worldguard.LocalPlayer"),
                        Class.forName("com.sk89q.worldguard.protection.flags.StateFlag"))
                        .invoke(applicableRegions, wgPlayer, buildFlag);

                    if (result != null && "DENY".equals(result.toString())) {
                        return false;
                    }
                } catch (Exception flagError) {
                    // Fallback: nếu có region và không check được flag thì deny
                    return false;
                }
            }

            return true; // Cho phép nếu không có region hoặc có quyền

        } catch (Exception e) {
            // Nếu có lỗi thì không cho phép (safe fallback)
            plugin.getLogger().warning("Error checking WorldGuard permissions: " + e.getMessage());
            return false;
        }
    }

    /**
     * Kiểm tra WorldGuard cho block cụ thể
     */
    private boolean canBreakBlockWorldGuard(Location location) {
        // Tương tự như canUseWorldGuard nhưng cho block cụ thể
        return canUseWorldGuard(null, location);
    }

    /**
     * Kiểm tra Superior Skyblock permissions
     */
    private boolean canUseSuperiorSkyblock(Player player, Location location) {
        try {
            // Kiểm tra xem SuperiorSkyblock có tồn tại không
            if (Bukkit.getPluginManager().getPlugin("SuperiorSkyblock2") == null) {
                return true; // Không có SuperiorSkyblock = cho phép
            }

            // Sử dụng reflection để kiểm tra SuperiorSkyblock
            Class<?> superiorSkyblockAPIClass = Class.forName("com.bgsoftware.superiorskyblock.api.SuperiorSkyblockAPI");

            // Lấy GridManager
            Object gridManager = superiorSkyblockAPIClass.getMethod("getGrid").invoke(null);

            // Lấy island tại location
            Object island = gridManager.getClass().getMethod("getIslandAt", Location.class)
                .invoke(gridManager, location);

            if (island == null) {
                return true; // Không có island = cho phép (wilderness)
            }

            // Kiểm tra xem player có phải member của island không
            Class<?> islandClass = Class.forName("com.bgsoftware.superiorskyblock.api.island.Island");
            Object superiorPlayer = superiorSkyblockAPIClass.getMethod("getPlayer", java.util.UUID.class)
                .invoke(null, player.getUniqueId());

            boolean isMember = (Boolean) islandClass.getMethod("isMember",
                Class.forName("com.bgsoftware.superiorskyblock.api.wrappers.SuperiorPlayer"))
                .invoke(island, superiorPlayer);

            return isMember; // Chỉ cho phép nếu là member của island

        } catch (Exception e) {
            // Nếu có lỗi thì cho phép (fallback)
            return true;
        }
    }

    /**
     * Kiểm tra Superior Skyblock cho block cụ thể
     */
    private boolean canBreakBlockSuperiorSkyblock(Location location) {
        try {
            // Kiểm tra xem SuperiorSkyblock có tồn tại không
            if (Bukkit.getPluginManager().getPlugin("SuperiorSkyblock2") == null) {
                return true; // Không có SuperiorSkyblock = cho phép
            }

            // Sử dụng reflection để kiểm tra SuperiorSkyblock
            Class<?> superiorSkyblockAPIClass = Class.forName("com.bgsoftware.superiorskyblock.api.SuperiorSkyblockAPI");

            // Lấy island tại location
            Object island = superiorSkyblockAPIClass.getMethod("getIslandAt", Location.class)
                .invoke(null, location);

            // Nếu không có island thì cho phép (wilderness)
            return island == null;

        } catch (Exception e) {
            // Nếu có lỗi thì cho phép (fallback)
            return true;
        }
    }

    /**
     * Lấy display name của item
     */
    private String getItemDisplayName(ItemStack item) {
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return item.getItemMeta().getDisplayName();
        }

        // Fallback to material name
        String materialName = item.getType().name().toLowerCase().replace("_", " ");
        return materialName.substring(0, 1).toUpperCase() + materialName.substring(1);
    }

    /**
     * Cleanup khi plugin disable
     */
    public void cleanup() {
        for (FarmingZoneInstance instance : activeFarmingZones.values()) {
            if (instance.getTask() != null) {
                instance.getTask().cancel();
            }
            instance.getHologram().remove();
        }
        activeFarmingZones.clear();
    }
    
    /**
     * Inner class để track farming zone instance
     */
    private static class FarmingZoneInstance {
        private final Player player;
        private final FarmingZoneHologram hologram;
        private final double farmingRadius;
        private final int duration;
        private BukkitTask task;

        public FarmingZoneInstance(Player player, FarmingZoneHologram hologram, double farmingRadius, int duration) {
            this.player = player;
            this.hologram = hologram;
            this.farmingRadius = farmingRadius;
            this.duration = duration;
        }
        
        public Player getPlayer() { return player; }
        public FarmingZoneHologram getHologram() { return hologram; }
        public double getFarmingRadius() { return farmingRadius; }
        public int getDuration() { return duration; }
        public BukkitTask getTask() { return task; }
        public void setTask(BukkitTask task) { this.task = task; }
    }
}
