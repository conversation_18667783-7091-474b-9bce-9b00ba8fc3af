package shyrcs.shopkeeper.gui.handlers;

import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.gui.ShopkeeperGUIManager;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

/**
 * Handles edit GUI interactions
 */
public class EditGUIHandler {
    
    private final SoulShopkeeperPlugin plugin;
    
    public EditGUIHandler(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * <PERSON><PERSON> clicks in edit GUI
     */
    public void handleEditGUIClick(Player player, InventoryClickEvent event, ShopkeeperGUIManager.GUISession session) {
        // Cancel ALL clicks to prevent item theft
        event.setCancelled(true);

        int slot = event.getSlot();
        SoulShopkeeper shopkeeper = session.getShopkeeper();

        if (shopkeeper == null) {
            player.closeInventory();
            return;
        }

        // Only handle clicks in the top inventory (GUI)
        if (!event.getClickedInventory().equals(event.getView().getTopInventory())) {
            return;
        }

        // Handle trade slots (10-43)
        if (isTradeSlot(slot)) {
            handleTradeSlotClick(player, event, shopkeeper, slot);
            return;
        }
        
        // Handle control buttons
        switch (slot) {
            case 45: // Toggle active
                toggleShopkeeperActive(player, shopkeeper);
                break;

            case 46: // Edit Name
                player.closeInventory();
                startNameEditConversation(player, shopkeeper);
                break;

            case 47: // Trade Mode Selection
                // Don't close inventory immediately - let the new GUI handle it
                org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    plugin.getGUIManager().openTradeModeSelection(player, shopkeeper);
                }, 1L);
                break;

            case 49: // Close
                player.closeInventory();
                break;

            case 53: // Delete shopkeeper
                handleShopkeeperDeletion(player, event, shopkeeper);
                break;

            default:
                // Do nothing for other slots
                break;
        }
    }
    
    /**
     * Handles trade slot clicks
     */
    private void handleTradeSlotClick(Player player, InventoryClickEvent event, SoulShopkeeper shopkeeper, int slot) {
        int tradeIndex = getTradeIndex(slot);
        
        if (event.isLeftClick()) {
            // Edit trade - use mode-based editing
            plugin.getGUIManager().openTradeEditByMode(player, shopkeeper, tradeIndex);
        } else if (event.isRightClick() && tradeIndex < shopkeeper.getTrades().size()) {
            // Toggle trade enabled (only for existing trades)
            ShopkeeperTrade trade = shopkeeper.getTrades().get(tradeIndex);
            trade.setEnabled(!trade.isEnabled());
            
            // Save changes
            saveShopkeeper(shopkeeper);
            
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&aTrade " + (tradeIndex + 1) + " " +
                                   (trade.isEnabled() ? "enabled" : "disabled"));

            // Refresh GUI with delay to prevent item duplication
            org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                plugin.getGUIManager().openEditGUI(player, shopkeeper);
            }, 1L);
        }
    }
    
    /**
     * Checks if slot is a trade slot
     */
    private boolean isTradeSlot(int slot) {
        // Trade slots are 10-43 (excluding borders and control buttons)
        return slot >= 10 && slot <= 43 && slot % 9 != 0 && slot % 9 != 8;
    }
    
    /**
     * Gets trade index from slot
     */
    private int getTradeIndex(int slot) {
        // Convert slot to trade index
        int row = slot / 9;
        int col = slot % 9;
        
        // Adjust for borders
        int adjustedRow = row - 1;
        int adjustedCol = col - 1;
        
        return adjustedRow * 7 + adjustedCol;
    }
    
    /**
     * Starts name edit conversation
     */
    private void startNameEditConversation(Player player, SoulShopkeeper shopkeeper) {
        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                               "&eEnter new name for shopkeeper (or 'cancel' to abort):");
        
        // Create conversation
        org.bukkit.conversations.ConversationFactory factory = new org.bukkit.conversations.ConversationFactory(plugin)
                .withModality(true)
                .withFirstPrompt(new NameEditPrompt(shopkeeper))
                .withTimeout(30)
                .thatExcludesNonPlayersWithMessage("Only players can edit shopkeeper names.");
        
        org.bukkit.conversations.Conversation conversation = factory.buildConversation(player);
        conversation.begin();
    }
    
    /**
     * Saves shopkeeper to database
     */
    private void saveShopkeeper(SoulShopkeeper shopkeeper) {
        try {
            if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                    (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
                wrapper.saveAll();
            } else {
                plugin.getDataManager().saveAll();
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to save shopkeeper: " + e.getMessage());
        }
    }
    
    /**
     * Name edit conversation prompt
     */
    private class NameEditPrompt extends org.bukkit.conversations.StringPrompt {
        private final SoulShopkeeper shopkeeper;
        
        public NameEditPrompt(SoulShopkeeper shopkeeper) {
            this.shopkeeper = shopkeeper;
        }
        
        @Override
        public String getPromptText(org.bukkit.conversations.ConversationContext context) {
            return MessageUtils.colorize("&eEnter new name:");
        }
        
        @Override
        public org.bukkit.conversations.Prompt acceptInput(org.bukkit.conversations.ConversationContext context, String input) {
            if (input.equalsIgnoreCase("cancel")) {
                context.getForWhom().sendRawMessage(MessageUtils.colorize(
                    plugin.getConfig().getString("messages.prefix") + "&cName edit cancelled."));
                return org.bukkit.conversations.Prompt.END_OF_CONVERSATION;
            }
            
            // Apply hex color support
            String coloredName = MessageUtils.colorize(input);
            shopkeeper.setName(coloredName);
            
            // Save changes
            saveShopkeeper(shopkeeper);
            
            // Refresh entity
            plugin.getEntityManager().refreshShopkeeperEntity(shopkeeper);
            
            context.getForWhom().sendRawMessage(MessageUtils.colorize(
                plugin.getConfig().getString("messages.prefix") + "&aShopkeeper name updated to: " + coloredName));
            
            return org.bukkit.conversations.Prompt.END_OF_CONVERSATION;
        }
    }

    /**
     * Toggles shopkeeper active status
     */
    private void toggleShopkeeperActive(Player player, SoulShopkeeper shopkeeper) {
        shopkeeper.setActive(!shopkeeper.isActive());

        saveShopkeeper(shopkeeper);

        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                               "&aShopkeeper " + (shopkeeper.isActive() ? "activated" : "deactivated"));

        // Refresh entity
        plugin.getEntityManager().refreshShopkeeperEntity(shopkeeper);

        // Refresh GUI with delay
        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
            plugin.getGUIManager().openEditGUI(player, shopkeeper);
        }, 1L);
    }

    /**
     * Handles shopkeeper deletion
     */
    private void handleShopkeeperDeletion(Player player, InventoryClickEvent event, SoulShopkeeper shopkeeper) {
        if (event.isShiftClick()) {
            try {
                // Remove entity first
                plugin.getEntityManager().removeShopkeeperEntity(shopkeeper.getId());

                // Remove from data
                boolean deleted = false;
                if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                    shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                        (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                    deleted = wrapper.getSQLiteDataManager().deleteShopkeeper(shopkeeper.getId());
                } else {
                    plugin.getDataManager().removeShopkeeper(shopkeeper.getId());
                    deleted = true;
                }

                player.closeInventory();

                if (deleted) {
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                           plugin.getConfig().getString("messages.shopkeeper-deleted"));
                } else {
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                           "&cFailed to delete shopkeeper from database!");
                }
            } catch (Exception e) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cError deleting shopkeeper: " + e.getMessage());
                plugin.getLogger().severe("Failed to delete shopkeeper " + shopkeeper.getId() + ": " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cShift-click to confirm deletion!");
        }
    }
}
