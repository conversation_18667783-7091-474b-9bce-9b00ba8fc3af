package shyrcs.shopkeeper.gui.handlers;

import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.gui.ShopkeeperGUIManager;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

/**
 * Handles edit GUI interactions
 */
public class EditGUIHandler {
    
    private final SoulShopkeeperPlugin plugin;
    
    public EditGUIHandler(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * <PERSON>les clicks in edit GUI
     */
    public void handleEditGUIClick(Player player, InventoryClickEvent event, ShopkeeperGUIManager.GUISession session) {
        int slot = event.getSlot();
        SoulShopkeeper shopkeeper = session.getShopkeeper();

        if (shopkeeper == null) {
            player.closeInventory();
            return;
        }

        // Only handle clicks in the top inventory (GUI)
        if (!event.getClickedInventory().equals(event.getView().getTopInventory())) {
            return;
        }

        // Handle trade slots based on mode
        if (isTradeSlot(slot, shopkeeper)) {
            if (shopkeeper.getTradeMode() == shyrcs.shopkeeper.enums.TradeMode.GUI) {
                // GUI mode: Allow item manipulation in crafting slots, but validate
                if (!isValidGUISlotInteraction(event, slot)) {
                    event.setCancelled(true);
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                           "&cInvalid item interaction!");
                    return;
                }
                // Don't cancel - allow item manipulation
            } else {
                // Native mode: Cancel all clicks, handle logic separately
                event.setCancelled(true);
            }
            handleTradeSlotClick(player, event, shopkeeper, slot);
            return;
        }

        // Cancel ALL other clicks to prevent item theft
        event.setCancelled(true);
        
        // Handle control buttons
        switch (slot) {
            case 45: // Toggle active
                toggleShopkeeperActive(player, shopkeeper);
                break;

            case 46: // Edit Name
                player.closeInventory();
                startNameEditConversation(player, shopkeeper);
                break;

            case 47: // Trade Mode Selection
                // Don't close inventory immediately - let the new GUI handle it
                org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    plugin.getGUIManager().openTradeModeSelection(player, shopkeeper);
                }, 1L);
                break;

            case 49: // Close
                player.closeInventory();
                break;

            case 53: // Delete shopkeeper
                handleShopkeeperDeletion(player, event, shopkeeper);
                break;

            default:
                // Do nothing for other slots
                break;
        }
    }
    
    /**
     * Handles trade slot clicks
     */
    private void handleTradeSlotClick(Player player, InventoryClickEvent event, SoulShopkeeper shopkeeper, int slot) {
        if (shopkeeper.getTradeMode() == shyrcs.shopkeeper.enums.TradeMode.GUI) {
            // GUI mode: allow direct item manipulation in crafting slots
            handleGUITradeSlotClick(player, event, shopkeeper, slot);
        } else {
            // Native mode: traditional trade list behavior
            int tradeIndex = getTradeIndex(slot);

            if (event.isLeftClick()) {
                // Edit trade - use mode-based editing
                plugin.getGUIManager().openTradeEditByMode(player, shopkeeper, tradeIndex);
            } else if (event.isRightClick() && tradeIndex < shopkeeper.getTrades().size()) {
                // Toggle trade enabled (only for existing trades)
                ShopkeeperTrade trade = shopkeeper.getTrades().get(tradeIndex);
                trade.setEnabled(!trade.isEnabled());

                // Save changes
                saveShopkeeper(shopkeeper);

                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&aTrade " + (tradeIndex + 1) + " " +
                                       (trade.isEnabled() ? "enabled" : "disabled"));

                // Refresh GUI with delay to prevent item duplication
                org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    plugin.getGUIManager().openEditGUI(player, shopkeeper);
                }, 1L);
            }
        }
    }

    /**
     * Validates GUI slot interactions
     */
    private boolean isValidGUISlotInteraction(InventoryClickEvent event, int slot) {
        // Prevent shift-clicking from player inventory to GUI slots
        if (event.getClick().isShiftClick() && event.getRawSlot() >= 54) {
            return false;
        }

        // Prevent number key swapping
        if (event.getClick().isKeyboardClick()) {
            return false;
        }

        // Allow normal left/right clicks and drag
        return true;
    }

    /**
     * Handles GUI mode trade slot clicks (direct item manipulation)
     */
    private void handleGUITradeSlotClick(Player player, InventoryClickEvent event, SoulShopkeeper shopkeeper, int slot) {
        // Allow item manipulation in GUI mode
        // Don't cancel the event - let players place/remove items directly

        // Schedule save after item manipulation with validation
        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
            // Validate player and inventory still exist
            if (player.isOnline() && player.getOpenInventory() != null) {
                try {
                    saveGUITradeFromSlots(player, event.getInventory(), shopkeeper);
                } catch (Exception e) {
                    plugin.getLogger().warning("Error saving GUI trade for player " + player.getName() + ": " + e.getMessage());
                }
            }
        }, 1L);
    }

    /**
     * Saves GUI trade from current slot contents
     * Hỗ trợ tất cả 9 crafting slots (10,11,12,19,20,21,28,29,30) + result slot (24)
     */
    private void saveGUITradeFromSlots(Player player, org.bukkit.inventory.Inventory inventory, SoulShopkeeper shopkeeper) {
        // Validation checks
        if (inventory == null || inventory.getSize() < 54) {
            plugin.getLogger().warning("Invalid inventory for GUI trade save - player: " + player.getName());
            return;
        }

        if (shopkeeper == null) {
            plugin.getLogger().warning("Shopkeeper is null for GUI trade save - player: " + player.getName());
            return;
        }

        // Crafting grid slots: 10,11,12,19,20,21,28,29,30
        int[] craftingSlots = {10, 11, 12, 19, 20, 21, 28, 29, 30};
        int resultSlot = 24;

        // Collect all non-empty crafting items with validation
        java.util.List<org.bukkit.inventory.ItemStack> ingredients = new java.util.ArrayList<>();

        for (int slot : craftingSlots) {
            try {
                org.bukkit.inventory.ItemStack item = inventory.getItem(slot);
                if (item != null &&
                    item.getType() != org.bukkit.Material.WHITE_STAINED_GLASS_PANE &&
                    item.getType() != org.bukkit.Material.YELLOW_STAINED_GLASS_PANE &&
                    item.getType() != org.bukkit.Material.GRAY_STAINED_GLASS_PANE &&
                    !item.getType().isAir()) {
                    ingredients.add(item.clone());
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Error reading slot " + slot + " for player " + player.getName() + ": " + e.getMessage());
            }
        }

        // Get result item with validation
        org.bukkit.inventory.ItemStack result = null;
        try {
            result = inventory.getItem(resultSlot);
        } catch (Exception e) {
            plugin.getLogger().warning("Error reading result slot for player " + player.getName() + ": " + e.getMessage());
        }

        // Ensure we have at least one trade
        if (shopkeeper.getTrades().isEmpty()) {
            shopkeeper.addTrade(new ShopkeeperTrade("", "", "", -1, 0, true));
        }

        // Update first trade
        ShopkeeperTrade trade = shopkeeper.getTrades().get(0);

        try {
            // Set ingredients (lấy tối đa 2 ingredients đầu tiên)
            if (ingredients.size() >= 1) {
                String itemId = plugin.getMMOItemStorage().storeMMOItem(ingredients.get(0));
                trade.setIngredient1Id(itemId != null ? itemId : "");
            } else {
                trade.setIngredient1Id("");
            }

            if (ingredients.size() >= 2) {
                String itemId = plugin.getMMOItemStorage().storeMMOItem(ingredients.get(1));
                trade.setIngredient2Id(itemId != null ? itemId : "");
            } else {
                trade.setIngredient2Id("");
            }

            // Set result
            if (result != null &&
                result.getType() != org.bukkit.Material.WHITE_STAINED_GLASS_PANE &&
                result.getType() != org.bukkit.Material.YELLOW_STAINED_GLASS_PANE &&
                result.getType() != org.bukkit.Material.GRAY_STAINED_GLASS_PANE &&
                !result.getType().isAir()) {
                String itemId = plugin.getMMOItemStorage().storeMMOItem(result);
                trade.setResultItemId(itemId != null ? itemId : "");
            } else {
                trade.setResultItemId("");
            }

            // Save shopkeeper
            saveShopkeeper(shopkeeper);

            // Feedback message
            String message = plugin.getConfig().getString("messages.prefix") + "&aTrade updated! ";
            if (ingredients.size() > 0) {
                message += "&7(" + ingredients.size() + " ingredients";
                if (result != null && !result.getType().isAir() &&
                    result.getType() != org.bukkit.Material.WHITE_STAINED_GLASS_PANE &&
                    result.getType() != org.bukkit.Material.YELLOW_STAINED_GLASS_PANE &&
                    result.getType() != org.bukkit.Material.GRAY_STAINED_GLASS_PANE) {
                    message += " → 1 result";
                }
                message += ")";
            }
            MessageUtils.sendMessage(player, message);

        } catch (Exception e) {
            plugin.getLogger().severe("Error saving GUI trade for player " + player.getName() + ": " + e.getMessage());
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cError saving trade! Please try again.");
        }
    }
    
    /**
     * Checks if slot is a trade slot based on shopkeeper mode
     */
    private boolean isTradeSlot(int slot, SoulShopkeeper shopkeeper) {
        if (shopkeeper.getTradeMode() == shyrcs.shopkeeper.enums.TradeMode.GUI) {
            // GUI mode: crafting slots (10,11,12,19,20,21,28,29,30) and result slot (24)
            int[] guiSlots = {10, 11, 12, 19, 20, 21, 28, 29, 30, 24};
            for (int guiSlot : guiSlots) {
                if (slot == guiSlot) {
                    return true;
                }
            }
            return false;
        } else {
            // Native mode: traditional trade slots (10-43, excluding borders)
            return slot >= 10 && slot <= 43 && slot % 9 != 0 && slot % 9 != 8;
        }
    }
    
    /**
     * Gets trade index from slot
     */
    private int getTradeIndex(int slot) {
        // Convert slot to trade index
        int row = slot / 9;
        int col = slot % 9;
        
        // Adjust for borders
        int adjustedRow = row - 1;
        int adjustedCol = col - 1;
        
        return adjustedRow * 7 + adjustedCol;
    }
    
    /**
     * Starts name edit conversation
     */
    private void startNameEditConversation(Player player, SoulShopkeeper shopkeeper) {
        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                               "&eEnter new name for shopkeeper (or 'cancel' to abort):");
        
        // Create conversation
        org.bukkit.conversations.ConversationFactory factory = new org.bukkit.conversations.ConversationFactory(plugin)
                .withModality(true)
                .withFirstPrompt(new NameEditPrompt(shopkeeper))
                .withTimeout(30)
                .thatExcludesNonPlayersWithMessage("Only players can edit shopkeeper names.");
        
        org.bukkit.conversations.Conversation conversation = factory.buildConversation(player);
        conversation.begin();
    }
    
    /**
     * Saves shopkeeper to database
     */
    private void saveShopkeeper(SoulShopkeeper shopkeeper) {
        try {
            if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                    (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
                wrapper.saveAll();
            } else {
                plugin.getDataManager().saveAll();
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to save shopkeeper: " + e.getMessage());
        }
    }
    
    /**
     * Name edit conversation prompt
     */
    private class NameEditPrompt extends org.bukkit.conversations.StringPrompt {
        private final SoulShopkeeper shopkeeper;
        
        public NameEditPrompt(SoulShopkeeper shopkeeper) {
            this.shopkeeper = shopkeeper;
        }
        
        @Override
        public String getPromptText(org.bukkit.conversations.ConversationContext context) {
            return MessageUtils.colorize("&eEnter new name:");
        }
        
        @Override
        public org.bukkit.conversations.Prompt acceptInput(org.bukkit.conversations.ConversationContext context, String input) {
            if (input.equalsIgnoreCase("cancel")) {
                context.getForWhom().sendRawMessage(MessageUtils.colorize(
                    plugin.getConfig().getString("messages.prefix") + "&cName edit cancelled."));
                return org.bukkit.conversations.Prompt.END_OF_CONVERSATION;
            }
            
            // Apply hex color support
            String coloredName = MessageUtils.colorize(input);
            shopkeeper.setName(coloredName);
            
            // Save changes
            saveShopkeeper(shopkeeper);
            
            // Refresh entity
            plugin.getEntityManager().refreshShopkeeperEntity(shopkeeper);
            
            context.getForWhom().sendRawMessage(MessageUtils.colorize(
                plugin.getConfig().getString("messages.prefix") + "&aShopkeeper name updated to: " + coloredName));
            
            return org.bukkit.conversations.Prompt.END_OF_CONVERSATION;
        }
    }

    /**
     * Toggles shopkeeper active status
     */
    private void toggleShopkeeperActive(Player player, SoulShopkeeper shopkeeper) {
        shopkeeper.setActive(!shopkeeper.isActive());

        saveShopkeeper(shopkeeper);

        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                               "&aShopkeeper " + (shopkeeper.isActive() ? "activated" : "deactivated"));

        // Refresh entity
        plugin.getEntityManager().refreshShopkeeperEntity(shopkeeper);

        // Refresh GUI with delay
        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
            plugin.getGUIManager().openEditGUI(player, shopkeeper);
        }, 1L);
    }

    /**
     * Handles shopkeeper deletion
     */
    private void handleShopkeeperDeletion(Player player, InventoryClickEvent event, SoulShopkeeper shopkeeper) {
        if (event.isShiftClick()) {
            try {
                // Remove entity first
                plugin.getEntityManager().removeShopkeeperEntity(shopkeeper.getId());

                // Remove from data
                boolean deleted = false;
                if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                    shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                        (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                    deleted = wrapper.getSQLiteDataManager().deleteShopkeeper(shopkeeper.getId());
                } else {
                    plugin.getDataManager().removeShopkeeper(shopkeeper.getId());
                    deleted = true;
                }

                player.closeInventory();

                if (deleted) {
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                           plugin.getConfig().getString("messages.shopkeeper-deleted"));
                } else {
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                           "&cFailed to delete shopkeeper from database!");
                }
            } catch (Exception e) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cError deleting shopkeeper: " + e.getMessage());
                plugin.getLogger().severe("Failed to delete shopkeeper " + shopkeeper.getId() + ": " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cShift-click to confirm deletion!");
        }
    }
}
