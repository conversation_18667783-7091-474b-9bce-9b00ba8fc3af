package shyrcs.shopkeeper.listeners;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityTeleportEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.player.PlayerInteractEntityEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.Material;
import org.bukkit.Bukkit;
import org.bukkit.conversations.*;
import org.bukkit.ChatColor;
import java.util.List;
import java.util.ArrayList;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.gui.SessionManager;
import shyrcs.shopkeeper.gui.ShopkeeperGUIManager;
import shyrcs.shopkeeper.gui.TradeViewManager;
import shyrcs.shopkeeper.gui.handlers.EditGUIHandler;
import shyrcs.shopkeeper.gui.handlers.TradeModeHandler;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

import java.util.UUID;

/**
 * Handles events related to shopkeepers and GUIs
 */
public class ShopkeeperListener implements Listener {

    private final SoulShopkeeperPlugin plugin;
    private final TradeViewManager tradeViewManager;
    private final EditGUIHandler editGUIHandler;
    private final TradeModeHandler tradeModeHandler;
    private final SessionManager sessionManager;
    
    public ShopkeeperListener(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
        this.tradeViewManager = new TradeViewManager(plugin);
        this.editGUIHandler = new EditGUIHandler(plugin);
        this.tradeModeHandler = new TradeModeHandler(plugin);
        this.sessionManager = new SessionManager(plugin);
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();

        // Check if this is a SoulShopkeeper GUI (excluding Trade Editor - handled separately)
        String title = event.getView().getTitle();
        if (!title.contains("Create Shopkeeper") &&
            !title.contains("Edit:") &&
            !title.contains("Select Trade Mode")) {
            return;
        }

        ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
        if (session == null) {
            event.setCancelled(true);
            return;
        }

        // Handle different click scenarios
        if (event.getClickedInventory() == null) {
            event.setCancelled(true);
            return;
        }

        try {
            switch (session.getType()) {
                case CREATION:
                    event.setCancelled(true);
                    if (event.getClickedInventory().equals(event.getView().getTopInventory())) {
                        handleCreationGUIClick(player, event, session);
                    }
                    break;
                case EDIT:
                    event.setCancelled(true);
                    if (event.getClickedInventory().equals(event.getView().getTopInventory())) {
                        editGUIHandler.handleEditGUIClick(player, event, session);
                    }
                    break;
                case TRADE_MODE:
                    event.setCancelled(true);
                    if (event.getClickedInventory().equals(event.getView().getTopInventory())) {
                        tradeModeHandler.handleTradeModeSelectionClick(player, event, session);
                    }
                    break;
                // TRADE_EDIT is now handled by TradeEditGUIListener
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Error handling GUI click: " + e.getMessage());
            e.printStackTrace();
            player.closeInventory();
        }
    }
    
    /**
     * Handles clicks in the creation GUI
     */
    private void handleCreationGUIClick(Player player, InventoryClickEvent event, ShopkeeperGUIManager.GUISession session) {
        int slot = event.getSlot();
        
        switch (slot) {
            case 13: // Villager shopkeeper
                createShopkeeper(player, "VILLAGER");
                break;
            case 21: // Zombie shopkeeper
                createShopkeeper(player, "ZOMBIE");
                break;
            case 23: // Skeleton shopkeeper
                createShopkeeper(player, "SKELETON");
                break;
            case 49: // Cancel
                player.closeInventory();
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + 
                                       "&cShopkeeper creation cancelled.");
                break;
        }
    }
    

    
    // Trade edit GUI handling moved to TradeEditGUIListener
    
    /**
     * Creates a new shopkeeper
     */
    private void createShopkeeper(Player player, String type) {
        // Check if player creation is allowed
        if (!plugin.getConfig().getBoolean("shopkeeper.allow-player-creation", true)) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cPlayer shopkeeper creation is disabled!");
            return;
        }

        // Check permission if required
        if (plugin.getConfig().getBoolean("shopkeeper.require-permission", true)) {
            if (!player.hasPermission("soulshopkeeper.create")) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       plugin.getConfig().getString("messages.no-permission"));
                return;
            }
        }

        // Check shopkeeper limits
        int maxShopkeepers = plugin.getConfig().getInt("shopkeeper.max-per-player", -1);
        if (maxShopkeepers > 0) { // Only check limit if it's not unlimited (-1)
            int currentCount = plugin.getDataManager().getPlayerShopkeeperCount(player.getUniqueId());

            if (currentCount >= maxShopkeepers) {
                String message = plugin.getConfig().getString("messages.max-shopkeepers-reached",
                               "&cYou have reached the maximum number of shopkeepers!");
                message = message.replace("{limit}", String.valueOf(maxShopkeepers));
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + message);
                return;
            }
        }
        
        // Create shopkeeper
        UUID id = UUID.randomUUID();
        String name = player.getName() + "'s Shopkeeper";
        SoulShopkeeper shopkeeper = new SoulShopkeeper(id, name, type, player.getLocation(), player.getUniqueId());
        
        // Add to data manager
        plugin.getDataManager().addShopkeeper(shopkeeper);

        // Spawn the entity
        plugin.getEntityManager().spawnShopkeeperEntity(shopkeeper);

        player.closeInventory();
        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                               plugin.getConfig().getString("messages.shopkeeper-created"));
        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                               "&7Use &e/soulshopkeeper edit &7or &eShift+Right-click &7to edit your shopkeeper!");
    }
    
    // saveTrade method moved to TradeEditGUIListener


    
    /**
     * Checks if a slot is a trade slot
     */
    private boolean isTradeSlot(int slot) {
        // Trade slots: 10-16, 19-25, 28-34 (excluding borders)
        return (slot >= 10 && slot <= 16 && slot != 17 && slot != 18) ||
               (slot >= 19 && slot <= 25 && slot != 26 && slot != 27) ||
               (slot >= 28 && slot <= 34 && slot != 35 && slot != 36);
    }

    /**
     * Gets the trade index from a slot
     */
    private int getTradeIndex(int slot) {
        if (slot >= 10 && slot <= 16) {
            return slot - 10;
        } else if (slot >= 19 && slot <= 25) {
            return (slot - 19) + 7;
        } else if (slot >= 28 && slot <= 34) {
            return (slot - 28) + 14;
        }
        return -1;
    }
    
    @EventHandler
    public void onInventoryDrag(org.bukkit.event.inventory.InventoryDragEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();

        // Get session to check mode
        ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());

        if (title.contains("Create Shopkeeper") || title.contains("Select Trade Mode")) {
            // Always cancel drag in creation and trade mode selection GUIs
            event.setCancelled(true);
        } else if (title.contains("Edit:") && session != null) {
            // For Edit GUI, check trade mode
            if (session.getShopkeeper().getTradeMode() == shyrcs.shopkeeper.enums.TradeMode.GUI) {
                // GUI mode: Allow drag in crafting slots only
                boolean allowDrag = false;
                int[] allowedSlots = {10, 11, 12, 19, 20, 21, 28, 29, 30, 24};

                for (int slot : event.getRawSlots()) {
                    for (int allowedSlot : allowedSlots) {
                        if (slot == allowedSlot) {
                            allowDrag = true;
                            break;
                        }
                    }
                    if (!allowDrag) break;
                }

                if (!allowDrag) {
                    event.setCancelled(true);
                }
            } else {
                // Native mode: Cancel all drags
                event.setCancelled(true);
            }
        }
        // Trade Editor drag events handled by TradeEditGUIListener
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // Check if the damaged entity is a shopkeeper
        if (plugin.getEntityManager().isShopkeeperEntity(event.getEntity())) {
            // Shopkeepers are always immortal - cancel ALL damage
            event.setCancelled(true);

            // Show message only to players who try to damage
            if (event.getDamager() instanceof Player) {
                Player player = (Player) event.getDamager();
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cShopkeepers are immortal and cannot be damaged!");
            }

            // Ensure health stays at maximum
            if (event.getEntity() instanceof org.bukkit.entity.LivingEntity) {
                org.bukkit.entity.LivingEntity living = (org.bukkit.entity.LivingEntity) event.getEntity();
                living.setHealth(living.getMaxHealth());
                living.setFireTicks(0);
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamage(org.bukkit.event.entity.EntityDamageEvent event) {
        // Check if the damaged entity is a shopkeeper
        if (plugin.getEntityManager().isShopkeeperEntity(event.getEntity())) {
            // Shopkeepers are always immortal - cancel ALL damage from any source
            event.setCancelled(true);

            // Ensure health stays at maximum
            if (event.getEntity() instanceof org.bukkit.entity.LivingEntity) {
                org.bukkit.entity.LivingEntity living = (org.bukkit.entity.LivingEntity) event.getEntity();
                living.setHealth(living.getMaxHealth());
                living.setFireTicks(0);
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityTeleport(EntityTeleportEvent event) {
        // Check if the teleporting entity is a shopkeeper
        if (plugin.getEntityManager().isShopkeeperEntity(event.getEntity())) {
            // Cancel all teleports except those we initiate for looking behavior
            // We'll handle position locking in the position monitor
            event.setCancelled(true);
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerInteractEntity(org.bukkit.event.player.PlayerInteractEntityEvent event) {
        // Check if the entity is a shopkeeper
        if (plugin.getEntityManager().isShopkeeperEntity(event.getRightClicked())) {
            // Handle shopkeeper interaction
            Player player = event.getPlayer();
            event.setCancelled(true); // Always cancel to prevent vanilla interactions

            // Get shopkeeper ID from entity
            java.util.UUID shopkeeperId = plugin.getEntityManager().getShopkeeperIdFromEntity(event.getRightClicked());
            if (shopkeeperId != null) {
                SoulShopkeeper shopkeeper = plugin.getDataManager().getShopkeeper(shopkeeperId);
                if (shopkeeper != null) {
                    // Check if player is owner and shift-clicking for edit
                    if (player.isSneaking() && (shopkeeper.isOwnedBy(player.getUniqueId()) || player.hasPermission("soulshopkeeper.admin"))) {
                        // Owner/Admin shift-clicking opens edit GUI
                        plugin.getGUIManager().openEditGUI(player, shopkeeper);
                    } else {
                        // Regular interaction opens trading interface
                        openTradingGUI(player, shopkeeper);
                    }
                }
            }
        }
    }

    /**
     * Opens the trading GUI for customers based on shopkeeper's trade mode
     */
    private void openTradingGUI(Player player, SoulShopkeeper shopkeeper) {
        // Check if shopkeeper is active
        if (!shopkeeper.isActive()) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cThis shopkeeper is currently inactive!");
            return;
        }

        // Check if shopkeeper has any enabled trades
        List<ShopkeeperTrade> enabledTrades = shopkeeper.getTrades().stream()
                .filter(ShopkeeperTrade::isEnabled)
                .collect(java.util.stream.Collectors.toList());

        if (enabledTrades.isEmpty()) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cThis shopkeeper has no available trades!");
            return;
        }

        // Open trading interface based on shopkeeper's trade mode
        if (shopkeeper.getTradeMode() == shyrcs.shopkeeper.enums.TradeMode.GUI) {
            // GUI Mode: Show custom trade GUI with result items
            openGUITrading(player, shopkeeper, enabledTrades);
        } else {
            // Native Mode: Open Minecraft's native trading GUI
            openNativeTrading(player, shopkeeper, enabledTrades);
        }
    }

    /**
     * Opens GUI trading interface (shows result items, click to view recipe)
     */
    private void openGUITrading(Player player, SoulShopkeeper shopkeeper, List<ShopkeeperTrade> enabledTrades) {
        String title = MessageUtils.colorize("&6&l" + shopkeeper.getName() + " - Trades");
        Inventory gui = Bukkit.createInventory(null, 54, title);

        // Fill with gray glass
        ItemStack grayGlass = createItem(Material.GRAY_STAINED_GLASS_PANE, " ");
        for (int i = 0; i < 54; i++) {
            gui.setItem(i, grayGlass);
        }

        // Set gray glass at specific slots (13, 22, 31)
        int[] grayGlassSlots = {13, 22, 31};
        for (int graySlot : grayGlassSlots) {
            gui.setItem(graySlot, grayGlass);
        }

        // Display enabled trades as result items
        int[] tradeSlots = {10, 11, 12, 14, 15, 16, 19, 20, 21, 23, 24, 25, 28, 29, 30, 32, 33, 34}; // Skip 13, 22, 31
        int tradeSlotIndex = 0;

        for (int i = 0; i < enabledTrades.size() && tradeSlotIndex < tradeSlots.length; i++) {
            ShopkeeperTrade trade = enabledTrades.get(i);
            ItemStack resultItem = trade.getResultItem();

            if (resultItem != null) {
                ItemStack display = resultItem.clone();
                ItemMeta meta = display.getItemMeta();
                if (meta != null) {
                    // Keep original name but add trade info
                    String originalName = meta.hasDisplayName() ? meta.getDisplayName() :
                                        display.getType().name().toLowerCase().replace('_', ' ');
                    meta.setDisplayName(MessageUtils.colorize("&e&l" + originalName));

                    List<String> lore = new ArrayList<>();
                    lore.add(MessageUtils.colorize("&7Trade #" + (i + 1)));
                    lore.add("");

                    // Show required ingredients
                    if (trade.getIngredient1Item() != null) {
                        String ing1Name = getItemDisplayName(trade.getIngredient1Item());
                        lore.add(MessageUtils.colorize("&7Requires: &f" + ing1Name));
                    }
                    if (trade.getIngredient2Item() != null) {
                        String ing2Name = getItemDisplayName(trade.getIngredient2Item());
                        lore.add(MessageUtils.colorize("&7+ &f" + ing2Name));
                    }

                    lore.add("");
                    lore.add(MessageUtils.colorize("&eClick to view recipe!"));
                    lore.add(MessageUtils.colorize("&7Right-click for quick trade"));

                    meta.setLore(lore);
                    display.setItemMeta(meta);
                }

                gui.setItem(tradeSlots[tradeSlotIndex], display);
                tradeSlotIndex++;
            }
        }

        // Info item
        gui.setItem(4, createItem(Material.EMERALD, "&a&lGUI Trading Mode",
                                 "&7Click items to view recipes",
                                 "&7Right-click for quick trade",
                                 "&7Total trades: &e" + enabledTrades.size()));

        // Create session to track this GUI
        plugin.getGUIManager().createTradeListSession(player, shopkeeper);

        player.openInventory(gui);
    }

    /**
     * Gets display name for an item
     */
    private String getItemDisplayName(ItemStack item) {
        if (item == null) return "None";

        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return MessageUtils.stripColors(item.getItemMeta().getDisplayName());
        }

        // Check if it's an MMOItem
        String mmoInfo = plugin.getMMOItemStorage().getMMOItemInfo(item);
        if (mmoInfo != null) {
            return mmoInfo;
        }

        return item.getType().name().toLowerCase().replace('_', ' ');
    }

    /**
     * Opens native Minecraft trading interface
     */
    private void openNativeTrading(Player player, SoulShopkeeper shopkeeper, List<ShopkeeperTrade> trades) {
        try {
            // Create a temporary merchant for trading
            org.bukkit.inventory.Merchant merchant = org.bukkit.Bukkit.createMerchant(shopkeeper.getName());

            // Convert our trades to Minecraft trades
            List<org.bukkit.inventory.MerchantRecipe> recipes = new java.util.ArrayList<>();

            for (ShopkeeperTrade trade : trades) {
                if (!trade.isEnabled()) continue;

                ItemStack result = trade.getResultItem();
                ItemStack ingredient1 = trade.getIngredient1Item();
                ItemStack ingredient2 = trade.getIngredient2Item();

                if (result == null || ingredient1 == null) {
                    plugin.getLogger().warning("Skipping invalid trade - missing result or ingredient1");
                    continue;
                }

                // Create flexible recipe that matches MMOItem type/id instead of exact NBT
                org.bukkit.inventory.MerchantRecipe recipe = createFlexibleRecipe(result, ingredient1, ingredient2);
                if (recipe == null) {
                    plugin.getLogger().warning("Failed to create recipe for trade");
                    continue;
                }

                recipes.add(recipe);
            }

            if (recipes.isEmpty()) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cNo valid trades available!");
                return;
            }

            merchant.setRecipes(recipes);

            // Open the trading interface
            player.openMerchant(merchant, true);

            plugin.getLogger().info("Opened trading GUI for player: " + player.getName() +
                                   " with shopkeeper: " + shopkeeper.getName() +
                                   " (" + recipes.size() + " trades)");

        } catch (Exception e) {
            plugin.getLogger().severe("Failed to open trading GUI: " + e.getMessage());
            e.printStackTrace();
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cFailed to open trading interface!");
        }
    }

    /**
     * Creates a flexible merchant recipe that matches MMOItem type/id instead of exact NBT
     */
    private org.bukkit.inventory.MerchantRecipe createFlexibleRecipe(ItemStack result, ItemStack ingredient1, ItemStack ingredient2) {
        try {
            // Create base recipe
            org.bukkit.inventory.MerchantRecipe recipe = new org.bukkit.inventory.MerchantRecipe(result, 999);

            // For MMOItems, we need to create "template" items that will match any item of the same type/id
            ItemStack flexibleIng1 = createFlexibleIngredient(ingredient1);
            if (flexibleIng1 != null) {
                recipe.addIngredient(flexibleIng1);
            } else {
                return null;
            }

            if (ingredient2 != null && ingredient2.getType() != Material.AIR) {
                ItemStack flexibleIng2 = createFlexibleIngredient(ingredient2);
                if (flexibleIng2 != null) {
                    recipe.addIngredient(flexibleIng2);
                }
            }

            return recipe;

        } catch (Exception e) {
            plugin.getLogger().warning("Failed to create flexible recipe: " + e.getMessage());
            return null;
        }
    }

    /**
     * Creates a flexible ingredient that matches MMOItem type/id
     */
    private ItemStack createFlexibleIngredient(ItemStack original) {
        if (original == null) return null;

        // Check if it's an MMOItem
        String mmoType = null;
        String mmoId = null;

        try {
            mmoType = net.Indyuce.mmoitems.MMOItems.getTypeName(original);
            mmoId = net.Indyuce.mmoitems.MMOItems.getID(original);
        } catch (Exception e) {
            // Not an MMOItem or MMOItems not available
        }

        if (mmoType != null && mmoId != null) {
            // It's an MMOItem - create a fresh template
            try {
                net.Indyuce.mmoitems.api.Type type = net.Indyuce.mmoitems.api.Type.get(mmoType);
                if (type != null) {
                    net.Indyuce.mmoitems.api.item.template.MMOItemTemplate template =
                        net.Indyuce.mmoitems.MMOItems.plugin.getTemplates().getTemplate(type, mmoId);
                    if (template != null) {
                        net.Indyuce.mmoitems.api.item.mmoitem.MMOItem mmoItem = template.newBuilder().build();
                        return mmoItem.newBuilder().build();
                    }
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to create MMOItem template for " + mmoType + ":" + mmoId);
            }
        }

        // Fallback to vanilla item (remove custom NBT but keep basic properties)
        ItemStack flexible = new ItemStack(original.getType(), original.getAmount());

        // Keep basic meta but remove custom NBT
        if (original.hasItemMeta()) {
            org.bukkit.inventory.meta.ItemMeta meta = flexible.getItemMeta();
            org.bukkit.inventory.meta.ItemMeta originalMeta = original.getItemMeta();

            if (originalMeta.hasDisplayName()) {
                meta.setDisplayName(originalMeta.getDisplayName());
            }
            if (originalMeta.hasLore()) {
                meta.setLore(originalMeta.getLore());
            }

            flexible.setItemMeta(meta);
        }

        return flexible;
    }

    /**
     * Creates an ItemStack with name and lore
     */
    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        if (meta != null) {
            meta.setDisplayName(MessageUtils.colorize(name));

            if (lore.length > 0) {
                List<String> coloredLore = new ArrayList<>();
                for (String line : lore) {
                    coloredLore.add(MessageUtils.colorize(line));
                }
                meta.setLore(coloredLore);
            }

            item.setItemMeta(meta);
        }

        return item;
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        sessionManager.handleInventoryClose(event);
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // Clean up GUI session when player quits
        plugin.getGUIManager().removeSession(event.getPlayer().getUniqueId());
    }
    







    /**
     * Starts a conversation to edit shopkeeper name
     */
    private void startNameEditConversation(Player player, SoulShopkeeper shopkeeper) {
        ConversationFactory factory = new ConversationFactory(plugin)
                .withModality(true)
                .withPrefix(new NameEditPrefix())
                .withFirstPrompt(new NameEditPrompt(shopkeeper))
                .withEscapeSequence("cancel")
                .withTimeout(60)
                .thatExcludesNonPlayersWithMessage("Only players can edit shopkeeper names!")
                .addConversationAbandonedListener(new NameEditAbandonedListener(player, shopkeeper));

        Conversation conversation = factory.buildConversation(player);
        conversation.begin();
    }

    /**
     * Conversation prefix for name editing
     */
    private static class NameEditPrefix implements ConversationPrefix {
        @Override
        public String getPrefix(ConversationContext context) {
            return ChatColor.GOLD + "[SoulShopkeeper] " + ChatColor.RESET;
        }
    }

    /**
     * Prompt for editing shopkeeper name
     */
    private class NameEditPrompt extends StringPrompt {
        private final SoulShopkeeper shopkeeper;

        public NameEditPrompt(SoulShopkeeper shopkeeper) {
            this.shopkeeper = shopkeeper;
        }

        @Override
        public String getPromptText(ConversationContext context) {
            return ChatColor.YELLOW + "Enter new name for your shopkeeper:\n" +
                   ChatColor.GRAY + "Current name: " + ChatColor.WHITE + shopkeeper.getName() + "\n" +
                   ChatColor.GRAY + "Type 'cancel' to cancel.";
        }

        @Override
        public Prompt acceptInput(ConversationContext context, String input) {
            if (input == null || input.trim().isEmpty()) {
                return new NameEditPrompt(shopkeeper);
            }

            String newName = MessageUtils.colorize(input.trim());

            // Validate name length
            if (newName.length() > 32) {
                context.getForWhom().sendRawMessage(ChatColor.RED + "Name too long! Maximum 32 characters.");
                return new NameEditPrompt(shopkeeper);
            }

            // Update shopkeeper name
            String oldName = shopkeeper.getName();
            shopkeeper.setName(newName);

            // Save changes
            try {
                if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                    shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                        (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                    wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
                } else {
                    plugin.getDataManager().saveAll();
                }

                // Update entity name
                plugin.getEntityManager().refreshShopkeeperEntity(shopkeeper);

                context.getForWhom().sendRawMessage(ChatColor.GREEN + "Shopkeeper name changed from '" +
                                                  oldName + "' to '" + newName + "'!");

                // Reopen edit GUI after a short delay
                if (context.getForWhom() instanceof Player) {
                    Player player = (Player) context.getForWhom();
                    plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                        plugin.getGUIManager().openEditGUI(player, shopkeeper);
                    }, 20L);
                }

            } catch (Exception e) {
                context.getForWhom().sendRawMessage(ChatColor.RED + "Failed to save name change: " + e.getMessage());
                shopkeeper.setName(oldName); // Revert on error
            }

            return Prompt.END_OF_CONVERSATION;
        }
    }

    /**
     * Listener for when name edit conversation is abandoned
     */
    private class NameEditAbandonedListener implements ConversationAbandonedListener {
        private final Player player;
        private final SoulShopkeeper shopkeeper;

        public NameEditAbandonedListener(Player player, SoulShopkeeper shopkeeper) {
            this.player = player;
            this.shopkeeper = shopkeeper;
        }

        @Override
        public void conversationAbandoned(ConversationAbandonedEvent event) {
            if (event.gracefulExit()) {
                return; // Normal completion
            }

            // Cancelled or timed out
            player.sendMessage(ChatColor.YELLOW + "Name editing cancelled.");

            // Reopen edit GUI
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                plugin.getGUIManager().openEditGUI(player, shopkeeper);
            }, 5L);
        }
    }









    /**
     * Finds the nearest shopkeeper owned by the player
     */
    private SoulShopkeeper findNearestOwnedShopkeeper(Player player) {
        java.util.Collection<SoulShopkeeper> playerShopkeepers = plugin.getDataManager().getPlayerShopkeepers(player.getUniqueId());
        SoulShopkeeper nearest = null;
        double minDistance = 10.0; // 10 block radius

        for (SoulShopkeeper shopkeeper : playerShopkeepers) {
            if (shopkeeper.getLocation().getWorld().equals(player.getWorld())) {
                double distance = shopkeeper.getLocation().distance(player.getLocation());
                if (distance <= minDistance) {
                    minDistance = distance;
                    nearest = shopkeeper;
                }
            }
        }

        return nearest;
    }



    /**
     * Saves shopkeeper to database
     */
    private void saveShopkeeper(SoulShopkeeper shopkeeper) {
        try {
            if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                    (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
                wrapper.saveAll();
            } else {
                plugin.getDataManager().saveAll();
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to save shopkeeper: " + e.getMessage());
        }
    }
}
