package shyrcs.shopkeeper.gui;

import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryCloseEvent;
import shyrcs.shopkeeper.SoulShopkeeperPlugin;

import java.util.UUID;

/**
 * Manages GUI sessions and cleanup
 */
public class SessionManager {
    
    private final SoulShopkeeperPlugin plugin;
    
    public SessionManager(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Handles inventory close events and session cleanup
     */
    public void handleInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getPlayer();
        String title = event.getView().getTitle();
        UUID playerId = player.getUniqueId();
        
        plugin.getLogger().info("Inventory close detected - Player: " + player.getName() + 
                               ", Title: '" + title + "'");
        
        // Get current session
        ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(playerId);
        
        if (session == null) {
            plugin.getLogger().info("No session found for player: " + player.getName());
            return;
        }
        
        plugin.getLogger().info("Found session type: " + session.getType() + " for player: " + player.getName());
        
        // Handle different GUI types
        if (isTradeEditGUI(title)) {
            handleTradeEditClose(player, session);
        } else if (isTradeModeGUI(title)) {
            handleTradeModeClose(player, session);
        } else if (isTradeViewGUI(title)) {
            handleTradeViewClose(player, session);
        } else if (isEditGUI(title)) {
            handleEditGUIClose(player, session);
        } else {
            // Generic cleanup for other GUIs
            scheduleSessionCleanup(player, "Generic GUI");
        }
    }
    
    /**
     * Handles trade edit GUI close
     */
    private void handleTradeEditClose(Player player, ShopkeeperGUIManager.GUISession session) {
        if (session.getType() == ShopkeeperGUIManager.GUIType.TRADE_EDIT) {
            plugin.getLogger().info("TradeEdit GUI closed, keeping session for player: " + player.getName());
            // Keep session for trade edit - don't clean up immediately
        } else {
            scheduleSessionCleanup(player, "Non-TradeEdit GUI");
        }
    }
    
    /**
     * Handles trade mode GUI close
     */
    private void handleTradeModeClose(Player player, ShopkeeperGUIManager.GUISession session) {
        if (session.getType() == ShopkeeperGUIManager.GUIType.TRADE_MODE) {
            plugin.getLogger().info("Trade mode GUI closed, cleaning up session for player: " + player.getName());
            // Immediate cleanup for trade mode - no delay needed
            plugin.getGUIManager().removeSession(player.getUniqueId());
            plugin.getLogger().info("Removed TRADE_MODE session for player: " + player.getName());
        }
    }
    
    /**
     * Handles trade view GUI close
     */
    private void handleTradeViewClose(Player player, ShopkeeperGUIManager.GUISession session) {
        if (session.getType() == ShopkeeperGUIManager.GUIType.TRADE_VIEW ||
            session.getType() == ShopkeeperGUIManager.GUIType.TRADE_VIEW_MODE ||
            session.getType() == ShopkeeperGUIManager.GUIType.TRADE_LIST) {
            plugin.getLogger().info("Trade view GUI closed, cleaning up session for player: " + player.getName());
            scheduleSessionCleanup(player, "Trade View GUI");
        }
    }
    
    /**
     * Handles edit GUI close
     */
    private void handleEditGUIClose(Player player, ShopkeeperGUIManager.GUISession session) {
        if (session.getType() == ShopkeeperGUIManager.GUIType.EDIT) {
            plugin.getLogger().info("Edit GUI closed, scheduling delayed cleanup for player: " + player.getName());
            // Delayed cleanup for edit GUI to allow navigation to other GUIs
            scheduleSessionCleanup(player, "Edit GUI");
        }
    }
    
    /**
     * Schedules session cleanup with delay and conflict prevention
     */
    private void scheduleSessionCleanup(Player player, String reason) {
        plugin.getLogger().info(reason + " closed, scheduling session cleanup");

        UUID playerId = player.getUniqueId();

        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
            // Double-check player is still online
            if (!player.isOnline()) {
                plugin.getGUIManager().removeSession(playerId);
                plugin.getLogger().info("Player offline - removed session for: " + player.getName());
                return;
            }

            ShopkeeperGUIManager.GUISession currentSession = plugin.getGUIManager().getSession(playerId);

            if (currentSession != null) {
                // Check if player has opened a new GUI
                String currentTitle = player.getOpenInventory().getTitle();
                boolean hasNewGUI = currentTitle.contains("Create Shopkeeper") ||
                                   currentTitle.contains("Edit:") ||
                                   currentTitle.contains("Select Trade Mode") ||
                                   currentTitle.contains("Trade Editor");

                if (hasNewGUI) {
                    plugin.getLogger().info("Player has new GUI open - skipping cleanup for: " + player.getName());
                    return;
                }

                // Only remove if it's not a protected session type
                if (currentSession.getType() != ShopkeeperGUIManager.GUIType.TRADE_EDIT &&
                    currentSession.getType() != ShopkeeperGUIManager.GUIType.TRADE_MODE) {
                    plugin.getGUIManager().removeSession(playerId);
                    plugin.getLogger().info("Removed " + currentSession.getType() + " session for player: " + player.getName());
                } else {
                    plugin.getLogger().info("Skipped session removal - protected session type " + currentSession.getType() + " for player: " + player.getName());
                }
            } else {
                plugin.getLogger().info("No session to remove for player: " + player.getName());
            }
        }, 5L); // Increased delay to 5 ticks to avoid conflicts
    }
    
    /**
     * GUI type detection methods
     */
    private boolean isTradeEditGUI(String title) {
        return title.contains("Trade Editor") || title.contains("Edit Trade #");
    }
    
    private boolean isTradeModeGUI(String title) {
        return title.contains("Select Trade Mode");
    }
    
    private boolean isTradeViewGUI(String title) {
        return title.contains("Trade Recipe #") || 
               title.contains("Select Trade View Mode") || 
               title.contains("Select Trade to View");
    }
    
    private boolean isEditGUI(String title) {
        return title.contains("Edit:") && title.contains("Shopkeeper");
    }
}
