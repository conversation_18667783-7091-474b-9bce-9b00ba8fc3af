package shyrcs.shopkeeper.gui;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

import java.util.Arrays;
import java.util.List;

/**
 * Manages trade view mode selection and navigation
 */
public class TradeViewManager {
    
    private final SoulShopkeeperPlugin plugin;
    
    public TradeViewManager(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Opens trade view mode selection GUI
     */
    public void openTradeViewSelection(Player player, SoulShopkeeper shopkeeper) {
        // Check if shopkeeper has trades
        if (shopkeeper.getTrades().isEmpty()) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cThis shopkeeper has no trades to view!");
            return;
        }
        
        // Create selection GUI
        String title = MessageUtils.colorize("&6&lSelect Trade View Mode");
        Inventory gui = Bukkit.createInventory(null, 27, title);
        
        // Fill with glass
        ItemStack glass = createGlassPane(Material.GRAY_STAINED_GLASS_PANE, " ");
        for (int i = 0; i < 27; i++) {
            gui.setItem(i, glass);
        }
        
        // Trade GUI option
        gui.setItem(11, createItem(Material.CRAFTING_TABLE, 
                                  "&e&lTrade GUI Mode",
                                  "&7Custom recipe viewer",
                                  "&7• See ingredients in crafting grid",
                                  "&7• Click result to trade",
                                  "&7• Better for complex recipes",
                                  "",
                                  "&eClick to use Trade GUI"));
        
        // Native Trading option
        gui.setItem(15, createItem(Material.EMERALD,
                                  "&a&lNative Trading Mode", 
                                  "&7Minecraft villager style",
                                  "&7• Standard trading interface",
                                  "&7• Familiar to all players",
                                  "&7• Quick and simple",
                                  "",
                                  "&eClick to use Native Trading"));
        
        // Back button
        gui.setItem(22, createItem(Material.BARRIER,
                                  "&c&lBack",
                                  "&7Return to shopkeeper menu"));
        
        // Create session for tracking
        plugin.getGUIManager().createTradeViewModeSession(player, shopkeeper);
        
        player.openInventory(gui);
    }
    
    /**
     * Opens trade GUI selection (list of trades)
     */
    public void openTradeGUISelection(Player player, SoulShopkeeper shopkeeper) {
        if (shopkeeper.getTrades().isEmpty()) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cThis shopkeeper has no trades to view!");
            return;
        }
        
        // Create selection GUI for trades
        String title = MessageUtils.colorize("&6&lSelect Trade to View");
        Inventory gui = Bukkit.createInventory(null, 54, title);
        
        // Fill with glass
        ItemStack glass = createGlassPane(Material.GRAY_STAINED_GLASS_PANE, " ");
        for (int i = 0; i < 54; i++) {
            gui.setItem(i, glass);
        }
        
        // Add trades (max 35 trades, slots 10-44)
        int slot = 10;
        for (int i = 0; i < Math.min(shopkeeper.getTrades().size(), 35); i++) {
            // Skip border slots
            if (slot % 9 == 0 || slot % 9 == 8) {
                slot++;
                if (slot % 9 == 0 || slot % 9 == 8) slot++;
            }
            
            ShopkeeperTrade trade = shopkeeper.getTrades().get(i);
            ItemStack tradeItem = trade.getResultItem();
            
            if (tradeItem != null) {
                ItemStack display = tradeItem.clone();
                ItemMeta meta = display.getItemMeta();
                if (meta != null) {
                    meta.setDisplayName(MessageUtils.colorize("&e&lTrade #" + (i + 1)));
                    List<String> lore = Arrays.asList(
                        MessageUtils.colorize("&7Click to view recipe"),
                        MessageUtils.colorize("&7Status: " + (trade.isEnabled() ? "&aEnabled" : "&cDisabled")),
                        "",
                        MessageUtils.colorize("&eClick to view this trade!")
                    );
                    meta.setLore(lore);
                    display.setItemMeta(meta);
                }
                gui.setItem(slot, display);
            }
            slot++;
        }
        
        // Back button
        gui.setItem(49, createItem(Material.BARRIER, "&c&lBack", "&7Return to mode selection"));
        
        // Create session for tracking
        plugin.getGUIManager().createTradeListSession(player, shopkeeper);
        
        player.openInventory(gui);
    }
    
    /**
     * Opens native trading interface
     */
    public void openNativeTrading(Player player, SoulShopkeeper shopkeeper) {
        try {
            // Get enabled trades
            List<ShopkeeperTrade> enabledTrades = shopkeeper.getTrades().stream()
                    .filter(ShopkeeperTrade::isEnabled)
                    .collect(java.util.stream.Collectors.toList());
            
            if (enabledTrades.isEmpty()) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cThis shopkeeper has no available trades!");
                return;
            }
            
            // Create merchant
            org.bukkit.inventory.Merchant merchant = Bukkit.createMerchant(shopkeeper.getName());
            List<org.bukkit.inventory.MerchantRecipe> recipes = new java.util.ArrayList<>();
            
            for (ShopkeeperTrade trade : enabledTrades) {
                ItemStack result = trade.getResultItem();
                ItemStack ingredient1 = trade.getIngredient1Item();
                ItemStack ingredient2 = trade.getIngredient2Item();
                
                if (result == null || ingredient1 == null) {
                    continue;
                }
                
                org.bukkit.inventory.MerchantRecipe recipe = new org.bukkit.inventory.MerchantRecipe(result, 999);
                recipe.addIngredient(ingredient1);
                
                if (ingredient2 != null && ingredient2.getType() != Material.AIR) {
                    recipe.addIngredient(ingredient2);
                }
                
                recipes.add(recipe);
            }
            
            if (!recipes.isEmpty()) {
                merchant.setRecipes(recipes);
                player.openMerchant(merchant, true);
            }
            
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to open native trading: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Creates a glass pane item
     */
    private ItemStack createGlassPane(Material material, String name) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(MessageUtils.colorize(name));
            item.setItemMeta(meta);
        }
        return item;
    }
    
    /**
     * Creates an item with display name and lore
     */
    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(MessageUtils.colorize(name));
            if (lore.length > 0) {
                meta.setLore(Arrays.asList(lore).stream()
                    .map(MessageUtils::colorize)
                    .collect(java.util.stream.Collectors.toList()));
            }
            item.setItemMeta(meta);
        }
        return item;
    }
}
