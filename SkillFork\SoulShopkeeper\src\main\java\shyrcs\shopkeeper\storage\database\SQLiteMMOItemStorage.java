package shyrcs.shopkeeper.storage.database;

import org.bukkit.inventory.ItemStack;
import org.bukkit.Material;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.StoredMMOItem;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * SQLite implementation for MMOItem storage with NBT preservation
 * Provides better performance and reliability than YAML storage
 */
public class SQLiteMMOItemStorage {
    
    private final SoulShopkeeperPlugin plugin;
    private final DatabaseManager databaseManager;
    private final Logger logger;
    
    // Cache for better performance
    private final Map<String, StoredMMOItem> itemCache;
    private final Map<String, ItemStack> stackCache;
    
    // SQL statements
    private static final String INSERT_ITEM = 
        "INSERT OR REPLACE INTO %smmo_items (storage_id, mmo_type, mmo_id, material, amount, display_name, lore, stored_data, raw_nbt, timestamp) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    private static final String SELECT_ITEM = 
        "SELECT * FROM %smmo_items WHERE storage_id = ?";
    
    private static final String DELETE_ITEM = 
        "DELETE FROM %smmo_items WHERE storage_id = ?";
    
    private static final String SELECT_ALL_ITEMS = 
        "SELECT * FROM %smmo_items";
    
    private static final String CLEANUP_OLD_ITEMS = 
        "DELETE FROM %smmo_items WHERE timestamp < ?";
    
    public SQLiteMMOItemStorage(SoulShopkeeperPlugin plugin, DatabaseManager databaseManager) {
        this.plugin = plugin;
        this.databaseManager = databaseManager;
        this.logger = plugin.getLogger();
        this.itemCache = new ConcurrentHashMap<>();
        this.stackCache = new ConcurrentHashMap<>();
        
        // Load existing items into cache
        loadItemsIntoCache();
        
        // Start cleanup task
        startCleanupTask();
    }
    
    /**
     * Stores an MMOItem in the database with NBT preservation
     */
    public String storeMMOItem(ItemStack itemStack) {
        if (itemStack == null || itemStack.getType().isAir()) {
            return null;
        }
        
        try {
            // Create StoredMMOItem
            StoredMMOItem storedItem = StoredMMOItem.fromItemStack(itemStack);
            if (storedItem == null) {
                return null;
            }
            
            // Generate unique ID
            String storageId = generateStorageId();
            
            // Store in database
            String sql = String.format(INSERT_ITEM, databaseManager.getTablePrefix());
            
            try (Connection conn = databaseManager.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setString(1, storageId);
                stmt.setString(2, storedItem.getType());
                stmt.setString(3, storedItem.getItemId());
                stmt.setString(4, storedItem.getMaterial().name());
                stmt.setInt(5, storedItem.getAmount());
                stmt.setString(6, storedItem.getDisplayName());
                stmt.setString(7, serializeLore(storedItem.getLore()));
                stmt.setString(8, serializeStoredData(storedItem.getStoredData()));
                stmt.setString(9, storedItem.getRawNBT());
                stmt.setLong(10, storedItem.getTimestamp());
                
                stmt.executeUpdate();
                
                // Update cache
                itemCache.put(storageId, storedItem);
                stackCache.put(storageId, itemStack.clone());
                
                if (plugin.getConfig().getBoolean("general.debug", false)) {
                    logger.info("Stored MMOItem in database: " + storageId + 
                               " (Type: " + storedItem.getType() + ", ID: " + storedItem.getItemId() + ")");
                }
                
                return storageId;
            }
            
        } catch (SQLException e) {
            logger.severe("Failed to store MMOItem in database: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * Retrieves an MMOItem from the database
     */
    public ItemStack retrieveMMOItem(String storageId) {
        if (storageId == null || storageId.isEmpty()) {
            return null;
        }
        
        try {
            // Check cache first
            if (stackCache.containsKey(storageId)) {
                ItemStack cached = stackCache.get(storageId);
                if (cached != null) {
                    return cached.clone();
                }
            }
            
            // Load from database
            StoredMMOItem storedItem = loadStoredItem(storageId);
            if (storedItem == null) {
                return null;
            }
            
            // Reconstruct ItemStack
            ItemStack reconstructed = reconstructMMOItem(storedItem);
            if (reconstructed != null) {
                // Update cache
                stackCache.put(storageId, reconstructed.clone());
                
                if (plugin.getConfig().getBoolean("general.debug", false)) {
                    logger.info("Retrieved MMOItem from database: " + storageId);
                }
            }
            
            return reconstructed;
            
        } catch (Exception e) {
            logger.severe("Failed to retrieve MMOItem from database: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * Loads a StoredMMOItem from the database
     */
    private StoredMMOItem loadStoredItem(String storageId) throws SQLException {
        // Check cache first
        if (itemCache.containsKey(storageId)) {
            return itemCache.get(storageId);
        }
        
        String sql = String.format(SELECT_ITEM, databaseManager.getTablePrefix());
        
        try (Connection conn = databaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, storageId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    StoredMMOItem item = createStoredItemFromResultSet(rs);
                    
                    // Cache the item
                    if (item != null) {
                        itemCache.put(storageId, item);
                    }
                    
                    return item;
                }
            }
        }
        
        return null;
    }
    
    /**
     * Creates a StoredMMOItem from database ResultSet
     */
    private StoredMMOItem createStoredItemFromResultSet(ResultSet rs) throws SQLException {
        try {
            String type = rs.getString("mmo_type");
            String itemId = rs.getString("mmo_id");
            String materialName = rs.getString("material");
            int amount = rs.getInt("amount");
            String displayName = rs.getString("display_name");
            String loreData = rs.getString("lore");
            String storedData = rs.getString("stored_data");
            String rawNBT = rs.getString("raw_nbt");
            long timestamp = rs.getLong("timestamp");
            
            // Create StoredMMOItem using reflection or a new constructor
            // For now, we'll create it manually
            Material material;
            try {
                material = Material.valueOf(materialName);
            } catch (IllegalArgumentException e) {
                material = Material.STONE; // Fallback
            }
            
            // Use the new factory method
            return StoredMMOItem.fromDatabaseData(type, itemId, material, amount, displayName,
                                                deserializeLore(loreData), deserializeStoredData(storedData),
                                                rawNBT, timestamp);
            
        } catch (Exception e) {
            logger.warning("Failed to create StoredMMOItem from ResultSet: " + e.getMessage());
            return null;
        }
    }
    


    /**
     * Creates an MMOItem from type and ID
     */
    private ItemStack createMMOItemFromTypeAndId(String type, String itemId) {
        try {
            net.Indyuce.mmoitems.api.Type mmoType = net.Indyuce.mmoitems.api.Type.get(type);
            if (mmoType == null) {
                return null;
            }

            net.Indyuce.mmoitems.api.item.template.MMOItemTemplate template =
                net.Indyuce.mmoitems.MMOItems.plugin.getTemplates().getTemplate(mmoType, itemId);
            if (template == null) {
                return null;
            }

            net.Indyuce.mmoitems.api.item.mmoitem.MMOItem mmoItem = template.newBuilder().build();
            return mmoItem.newBuilder().build();

        } catch (Exception e) {
            logger.warning("Failed to create MMOItem " + type + "." + itemId + ": " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Reconstructs an MMOItem from stored data
     */
    private ItemStack reconstructMMOItem(StoredMMOItem storedItem) {
        try {
            // Get MMOItem type
            net.Indyuce.mmoitems.api.Type type = net.Indyuce.mmoitems.api.Type.get(storedItem.getType());
            if (type == null) {
                logger.warning("Unknown MMOItem type: " + storedItem.getType());
                return null;
            }

            // Get template
            net.Indyuce.mmoitems.api.item.template.MMOItemTemplate template =
                net.Indyuce.mmoitems.MMOItems.plugin.getTemplates().getTemplate(type, storedItem.getItemId());
            if (template == null) {
                logger.warning("MMOItem template not found: " + storedItem.getType() + "." + storedItem.getItemId());
                return null;
            }

            // Build the item with preserved data
            net.Indyuce.mmoitems.api.item.mmoitem.MMOItem mmoItem = template.newBuilder().build();

            // Apply stored modifications if available
            if (storedItem.hasStoredData()) {
                applyStoredModifications(mmoItem, storedItem);
            }

            // Build final ItemStack
            ItemStack result = mmoItem.newBuilder().build();

            // Set the correct amount from stored data
            result.setAmount(storedItem.getAmount());

            // Apply any additional NBT data that might have been lost
            if (storedItem.hasRawNBT()) {
                result = applyRawNBT(result, storedItem.getRawNBT());
                // Ensure amount is preserved after NBT application
                result.setAmount(storedItem.getAmount());
            }

            return result;

        } catch (Exception e) {
            logger.severe("Failed to reconstruct MMOItem: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Applies stored modifications to an MMOItem
     */
    private void applyStoredModifications(net.Indyuce.mmoitems.api.item.mmoitem.MMOItem mmoItem, StoredMMOItem storedItem) {
        // This method would apply any stored stat modifications, enchantments, etc.
        // Implementation depends on what specific data needs to be preserved

        Map<String, Object> storedData = storedItem.getStoredData();
        if (storedData != null && !storedData.isEmpty()) {
            // Apply stored stats, enchantments, etc.
            // This is where you would restore any custom modifications

            if (plugin.getConfig().getBoolean("general.debug", false)) {
                logger.info("Applying stored modifications for MMOItem: " + storedItem.getFullId());
            }
        }
    }

    /**
     * Applies raw NBT data to an ItemStack
     */
    private ItemStack applyRawNBT(ItemStack itemStack, String rawNBT) {
        // This method would apply raw NBT data if needed
        // Implementation depends on your specific NBT preservation needs

        if (rawNBT != null && !rawNBT.isEmpty()) {
            if (plugin.getConfig().getBoolean("general.debug", false)) {
                logger.info("Applying raw NBT data to item");
            }
            // Here you would apply the raw NBT data
            // For now, return the original item
        }

        return itemStack;
    }
    
    /**
     * Deletes an item from the database
     */
    public boolean deleteMMOItem(String storageId) {
        if (storageId == null || storageId.isEmpty()) {
            return false;
        }
        
        try {
            String sql = String.format(DELETE_ITEM, databaseManager.getTablePrefix());
            
            try (Connection conn = databaseManager.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setString(1, storageId);
                int affected = stmt.executeUpdate();
                
                // Remove from cache
                itemCache.remove(storageId);
                stackCache.remove(storageId);
                
                return affected > 0;
            }
            
        } catch (SQLException e) {
            logger.severe("Failed to delete MMOItem from database: " + e.getMessage());
            e.printStackTrace();
        }
        
        return false;
    }
    
    /**
     * Loads all items into cache for better performance
     */
    private void loadItemsIntoCache() {
        try {
            String sql = String.format(SELECT_ALL_ITEMS, databaseManager.getTablePrefix());
            
            try (Connection conn = databaseManager.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                int count = 0;
                while (rs.next()) {
                    String storageId = rs.getString("storage_id");
                    StoredMMOItem item = createStoredItemFromResultSet(rs);
                    
                    if (item != null) {
                        itemCache.put(storageId, item);
                        count++;
                    }
                }
                
                logger.info("Loaded " + count + " MMOItems into cache from database");
            }
            
        } catch (SQLException e) {
            logger.warning("Failed to load items into cache: " + e.getMessage());
        }
    }
    
    /**
     * Starts the cleanup task to remove old unused items
     */
    private void startCleanupTask() {
        long cleanupInterval = plugin.getConfig().getLong("storage.cleanup-interval", 24) * 60 * 60 * 20L; // hours to ticks
        
        plugin.getServer().getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            cleanupOldItems();
        }, cleanupInterval, cleanupInterval);
    }
    
    /**
     * Cleans up old unused items from the database
     */
    private void cleanupOldItems() {
        try {
            long cutoffTime = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000L); // 7 days ago
            String sql = String.format(CLEANUP_OLD_ITEMS, databaseManager.getTablePrefix());
            
            try (Connection conn = databaseManager.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setLong(1, cutoffTime);
                int deleted = stmt.executeUpdate();
                
                if (deleted > 0) {
                    logger.info("Cleaned up " + deleted + " old MMOItems from database");
                }
            }
            
        } catch (SQLException e) {
            logger.warning("Failed to cleanup old items: " + e.getMessage());
        }
    }
    
    /**
     * Generates a unique storage ID
     */
    private String generateStorageId() {
        return "mmo_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }
    
    /**
     * Serializes lore list to string
     */
    private String serializeLore(List<String> lore) {
        if (lore == null || lore.isEmpty()) {
            return null;
        }
        return String.join("||", lore);
    }
    
    /**
     * Deserializes lore string to list
     */
    private List<String> deserializeLore(String loreData) {
        if (loreData == null || loreData.isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.asList(loreData.split("\\|\\|"));
    }
    
    /**
     * Serializes stored data map to string using simple key=value format
     */
    private String serializeStoredData(Map<String, Object> storedData) {
        if (storedData == null || storedData.isEmpty()) {
            return null;
        }

        try {
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, Object> entry : storedData.entrySet()) {
                if (sb.length() > 0) {
                    sb.append("||");
                }
                sb.append(entry.getKey()).append("=").append(entry.getValue().toString());
            }
            return sb.toString();
        } catch (Exception e) {
            logger.warning("Failed to serialize stored data: " + e.getMessage());
            return null;
        }
    }

    /**
     * Deserializes stored data string to map using simple key=value format
     */
    private Map<String, Object> deserializeStoredData(String storedData) {
        if (storedData == null || storedData.isEmpty()) {
            return new HashMap<>();
        }

        try {
            Map<String, Object> result = new HashMap<>();
            String[] pairs = storedData.split("\\|\\|");

            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    result.put(keyValue[0], keyValue[1]);
                }
            }

            return result;
        } catch (Exception e) {
            logger.warning("Failed to deserialize stored data: " + e.getMessage());
            return new HashMap<>();
        }
    }
    
    /**
     * Gets cache statistics
     */
    public Map<String, Integer> getCacheStats() {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("cached_items", itemCache.size());
        stats.put("cached_stacks", stackCache.size());
        return stats;
    }
    
    /**
     * Clears the cache
     */
    public void clearCache() {
        itemCache.clear();
        stackCache.clear();
        logger.info("MMOItem cache cleared");
    }

    public SoulShopkeeperPlugin getPlugin() {
        return plugin;
    }
}
