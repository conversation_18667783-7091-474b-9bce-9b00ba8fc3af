package shyrcs.shopkeeper.gui;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

/**
 * Listener for Trade Recipe GUI interactions (the crafting-style GUI)
 */
public class TradeViewGUIListener implements Listener {
    
    private final SoulShopkeeperPlugin plugin;
    
    public TradeViewGUIListener(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();

        // Check if this is a trade recipe GUI (crafting-style)
        if (!title.contains("Trade Recipe #")) {
            return;
        }

        // ALWAYS cancel clicks in trade view GUI to prevent item theft
        event.setCancelled(true);

        // Get session
        ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
        if (session == null || session.getType() != ShopkeeperGUIManager.GUIType.TRADE_VIEW) {
            player.closeInventory();
            return;
        }
        
        int slot = event.getSlot();
        SoulShopkeeper shopkeeper = session.getShopkeeper();
        int tradeIndex = session.getTradeIndex();
        
        if (shopkeeper == null || tradeIndex < 0 || tradeIndex >= shopkeeper.getTrades().size()) {
            player.closeInventory();
            return;
        }
        
        ShopkeeperTrade trade = shopkeeper.getTrades().get(tradeIndex);

        // Validate trade before allowing any interaction
        if (!isTradeValid(trade)) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cThis trade is not properly configured!");
            player.closeInventory();
            return;
        }

        // Handle clicks
        switch (slot) {
            case 4: // Back button
                player.closeInventory();
                // Open shopkeeper trading GUI (native)
                openNativeTrading(player, shopkeeper);
                break;

            case 24: // Result item - start trade
                if (trade.getResultItem() != null && trade.isEnabled()) {
                    player.closeInventory();
                    // Open native trading interface for this specific trade
                    openNativeTradingForTrade(player, shopkeeper, trade);
                } else if (!trade.isEnabled()) {
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                           "&cThis trade is currently disabled!");
                } else {
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                           "&cThis trade has no result item!");
                }
                break;

            default:
                // Do nothing for other slots - all clicks are cancelled
                break;
        }
    }
    
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getPlayer();
        String title = event.getView().getTitle();
        
        if (title.contains("Trade Recipe #")) {
            // Clean up session
            plugin.getGUIManager().removeSession(player.getUniqueId());
        }
    }
    
    /**
     * Opens native trading interface with all trades
     */
    private void openNativeTrading(Player player, SoulShopkeeper shopkeeper) {
        try {
            // Get enabled trades
            java.util.List<ShopkeeperTrade> enabledTrades = shopkeeper.getTrades().stream()
                    .filter(ShopkeeperTrade::isEnabled)
                    .collect(java.util.stream.Collectors.toList());
            
            if (enabledTrades.isEmpty()) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cThis shopkeeper has no available trades!");
                return;
            }
            
            // Create merchant
            org.bukkit.inventory.Merchant merchant = org.bukkit.Bukkit.createMerchant(shopkeeper.getName());
            java.util.List<org.bukkit.inventory.MerchantRecipe> recipes = new java.util.ArrayList<>();
            
            for (ShopkeeperTrade trade : enabledTrades) {
                org.bukkit.inventory.ItemStack result = trade.getResultItem();
                org.bukkit.inventory.ItemStack ingredient1 = trade.getIngredient1Item();
                org.bukkit.inventory.ItemStack ingredient2 = trade.getIngredient2Item();
                
                if (result == null || ingredient1 == null) {
                    continue;
                }
                
                org.bukkit.inventory.MerchantRecipe recipe = new org.bukkit.inventory.MerchantRecipe(result, 999);
                recipe.addIngredient(ingredient1);
                
                if (ingredient2 != null && ingredient2.getType() != org.bukkit.Material.AIR) {
                    recipe.addIngredient(ingredient2);
                }
                
                recipes.add(recipe);
            }
            
            if (!recipes.isEmpty()) {
                merchant.setRecipes(recipes);
                player.openMerchant(merchant, true);
            }
            
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to open native trading: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Opens native trading interface for a specific trade
     */
    private void openNativeTradingForTrade(Player player, SoulShopkeeper shopkeeper, ShopkeeperTrade trade) {
        try {
            if (!trade.isEnabled()) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cThis trade is currently disabled!");
                return;
            }
            
            org.bukkit.inventory.ItemStack result = trade.getResultItem();
            org.bukkit.inventory.ItemStack ingredient1 = trade.getIngredient1Item();
            org.bukkit.inventory.ItemStack ingredient2 = trade.getIngredient2Item();
            
            if (result == null || ingredient1 == null) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cInvalid trade configuration!");
                return;
            }
            
            // Create merchant with single trade
            org.bukkit.inventory.Merchant merchant = org.bukkit.Bukkit.createMerchant(shopkeeper.getName());
            org.bukkit.inventory.MerchantRecipe recipe = new org.bukkit.inventory.MerchantRecipe(result, 999);
            recipe.addIngredient(ingredient1);
            
            if (ingredient2 != null && ingredient2.getType() != org.bukkit.Material.AIR) {
                recipe.addIngredient(ingredient2);
            }
            
            merchant.setRecipes(java.util.Arrays.asList(recipe));
            player.openMerchant(merchant, true);
            
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to open specific trade: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Validates if a trade is properly configured
     */
    private boolean isTradeValid(ShopkeeperTrade trade) {
        // Must have result item
        if (trade.getResultItem() == null) {
            return false;
        }

        // Must have at least one ingredient
        if (trade.getIngredient1Item() == null) {
            return false;
        }

        // Result and ingredients must not be air
        if (trade.getResultItem().getType().isAir()) {
            return false;
        }

        if (trade.getIngredient1Item().getType().isAir()) {
            return false;
        }

        // If ingredient2 exists, it must not be air
        if (trade.getIngredient2Item() != null && trade.getIngredient2Item().getType().isAir()) {
            return false;
        }

        return true;
    }
}
