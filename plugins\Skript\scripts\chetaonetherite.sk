command /chetaonetherite [<player>]:
    permission: chetaonetherite.use
    trigger:
        # determine target player
        if arg-1 is set:
            # command run from console
            set {_target} to arg-1
        else:
            # command run by player
            set {_target} to player
        
        if {_target} is not set:
            send "&cPlayer not found or command must be run by a player!" to console
            stop

        # Get quantities of materials needed
        set {_scrap_placeholder} to "exstorage_quantity_NETHERITE_SCRAP"
        set {_gold_placeholder} to "exstorage_quantity_GOLD_INGOT"
        
        set {_scrap_amount} to placeholder {_scrap_placeholder} for {_target} parsed as number
        set {_gold_amount} to placeholder {_gold_placeholder} for {_target} parsed as number
        
        # Check if player has materials
        if {_scrap_amount} < 4:
            send "&c%{_target}% không có đủ Vụn Netherit để chế tạo! Cần ít nhất 4 Vụn Netherit." to console
            send "&cBạn không có đủ <#EFC759>Vụn Netherit &cđể chế tạo! Cần ít nhất &e4 Vụn Netherit&c." to {_target}
            stop
            
        if {_gold_amount} < 4:
            send "&c%{_target}% không có đủ Phôi Vàng để chế tạo! <PERSON><PERSON>n ít nhất 4 Phôi Vàng." to console
            send "&cBạn không có đủ &ePhôi Vàng &cđể chế tạo! Cần ít nhất &e4 Phôi Vàng&c." to {_target}
            stop

        # Calculate maximum possible Phôi Netherits based on available materials
        set {_max_from_scrap} to floor({_scrap_amount} / 4)
        set {_max_from_gold} to floor({_gold_amount} / 4)
        
        # The limiting factor determines how many we can make
        if {_max_from_scrap} <= {_max_from_gold}:
            set {_max_netherite} to {_max_from_scrap}
        else:
            set {_max_netherite} to {_max_from_gold}

        set {_free_space} to placeholder "exstorage_free_space" for {_target} parsed as number
        
        if {_free_space} is -1:
            # Unlimited space
            set {_netherite_to_make} to {_max_netherite}
        else:
            # Limited space - check if we have enough free space
            if {_free_space} >= {_max_netherite}:
                set {_netherite_to_make} to {_max_netherite}
            else:
                set {_netherite_to_make} to {_free_space}
                
                if {_netherite_to_make} < 1:
                    send "&c%{_target}% không có đủ slot kho trống để chế tạo! Cần ít nhất 1 slot kho trống." to console
                    send "&cBạn không có đủ slot kho trống để chế tạo! Cần ít nhất &e1 slot kho trống &cđể chế tạo Phôi Netherit." to {_target}
                    stop

        # Calculate materials needed
        set {_scrap_needed} to {_netherite_to_make} * 4
        set {_gold_needed} to {_netherite_to_make} * 4
        
        # Execute the crafting
        execute console command "esadmin take NETHERITE_SCRAP %{_scrap_needed}% %{_target}%"
        execute console command "esadmin take GOLD_INGOT %{_gold_needed}% %{_target}%"
        execute console command "esadmin add NETHERITE_INGOT %{_netherite_to_make}% %{_target}%"

        # Send success messages
        if {_free_space} is -1:
            send "&a%{_target}% đã chế tạo thành công %{_netherite_to_make}% <#5B5A4A>Phôi Netherit &a(Sử dụng %{_scrap_needed}% Vụn Netherit + %{_gold_needed}% Phôi Vàng)" to console
            send "&aBạn đã chế tạo thành công &e%{_netherite_to_make}% <#5B5A4A>Phôi Netherit &a(Sử dụng &e%{_scrap_needed}% <#EFC759>Vụn Netherit &a+ &e%{_gold_needed}% &ePhôi Vàng&a)" to {_target}
        else:
            if {_netherite_to_make} < {_max_netherite}:
                set {_remaining_possible} to {_max_netherite} - {_netherite_to_make}
                send "&a%{_target}% đã chế tạo thành công %{_netherite_to_make}% <#5B5A4A>Phôi Netherit &a(Sử dụng %{_scrap_needed}% Vụn Netherit + %{_gold_needed}% Phôi Vàng) &7(Có thể chế tạo thêm %{_remaining_possible}% nếu có slot)" to console
                send "&aBạn đã chế tạo thành công &e%{_netherite_to_make}% <#5B5A4A>Phôi Netherit &a(Sử dụng &e%{_scrap_needed}% <#EFC759>Vụn Netherit &a+ &e%{_gold_needed}% &ePhôi Vàng&a) &7(Có thể chế tạo thêm &e%{_remaining_possible}% &7nếu có đủ slot kho)" to {_target}
            else:
                send "&a%{_target}% đã chế tạo thành công %{_netherite_to_make}% <#5B5A4A>Phôi Netherit &a(Sử dụng %{_scrap_needed}% Vụn Netherit + %{_gold_needed}% Phôi Vàng)" to console
                send "&aBạn đã chế tạo thành công &e%{_netherite_to_make}% <#5B5A4A>Phôi Netherit &a(Sử dụng &e%{_scrap_needed}% <#EFC759>Vụn Netherit &a+ &e%{_gold_needed}% &ePhôi Vàng&a)" to {_target}