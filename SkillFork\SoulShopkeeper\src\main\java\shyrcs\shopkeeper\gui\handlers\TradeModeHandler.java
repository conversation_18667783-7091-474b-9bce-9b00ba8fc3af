package shyrcs.shopkeeper.gui.handlers;

import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.enums.TradeMode;
import shyrcs.shopkeeper.gui.ShopkeeperGUIManager;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.utils.MessageUtils;

/**
 * Handles trade mode selection interactions
 */
public class TradeModeHandler {
    
    private final SoulShopkeeperPlugin plugin;
    
    public TradeModeHandler(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * <PERSON>les clicks in trade mode selection GUI
     */
    public void handleTradeModeSelectionClick(Player player, InventoryClickEvent event, ShopkeeperGUIManager.GUISession session) {
        // Cancel ALL clicks to prevent item theft
        event.setCancelled(true);

        int slot = event.getSlot();
        SoulShopkeeper shopkeeper = session.getShopkeeper();

        if (shopkeeper == null) {
            player.closeInventory();
            return;
        }

        // Only handle clicks in the top inventory (GUI)
        if (!event.getClickedInventory().equals(event.getView().getTopInventory())) {
            return;
        }

        switch (slot) {
            case 11: // GUI Mode
                setTradeMode(player, shopkeeper, TradeMode.GUI);
                break;
                
            case 15: // Native Mode
                setTradeMode(player, shopkeeper, TradeMode.NATIVE);
                break;
                
            case 22: // Back
                player.closeInventory();
                plugin.getGUIManager().openEditGUI(player, shopkeeper);
                break;
                
            default:
                // Do nothing for other slots
                break;
        }
    }
    
    /**
     * Sets trade mode and provides feedback
     */
    private void setTradeMode(Player player, SoulShopkeeper shopkeeper, TradeMode mode) {
        TradeMode oldMode = shopkeeper.getTradeMode();

        if (oldMode == mode) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&eAlready using " + mode.getDisplayName() + " mode!");
        } else {
            shopkeeper.setTradeMode(mode);
            saveShopkeeper(shopkeeper);

            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&aChanged from &e" + oldMode.getDisplayName() +
                                   " &ato &e" + mode.getDisplayName() + " &amode!");
        }

        // Clean up current session before closing
        plugin.getGUIManager().removeSession(player.getUniqueId());
        player.closeInventory();

        // Return to edit GUI with delay
        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
            plugin.getGUIManager().openEditGUI(player, shopkeeper);
        }, 2L);
    }
    
    /**
     * Saves shopkeeper to database
     */
    private void saveShopkeeper(SoulShopkeeper shopkeeper) {
        try {
            if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                    (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
                wrapper.saveAll();
            } else {
                plugin.getDataManager().saveAll();
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to save shopkeeper: " + e.getMessage());
        }
    }
}
