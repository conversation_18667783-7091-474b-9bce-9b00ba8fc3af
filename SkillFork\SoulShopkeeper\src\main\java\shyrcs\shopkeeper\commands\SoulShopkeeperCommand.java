package shyrcs.shopkeeper.commands;

import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;
import shyrcs.shopkeeper.utils.DatabaseTestUtils;

import java.util.*;

/**
 * Main command handler for SoulShopkeeper
 * Handles /soulshopkeeper command and all subcommands
 */
public class SoulShopkeeperCommand implements CommandExecutor, TabCompleter {
    
    private final SoulShopkeeperPlugin plugin;
    private final Map<String, SubCommand> subCommands;
    
    public SoulShopkeeperCommand(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
        this.subCommands = new HashMap<>();
        
        registerSubCommands();
    }
    
    private void registerSubCommands() {
        subCommands.put("create", new CreateCommand());
        subCommands.put("edit", new EditCommand());
        subCommands.put("delete", new DeleteCommand());
        subCommands.put("list", new ListCommand());
        subCommands.put("info", new InfoCommand());
        subCommands.put("model", new ModelCommand());
        subCommands.put("reload", new ReloadCommand());
        subCommands.put("cleanup", new CleanupCommand());
        subCommands.put("save", new SaveCommand());
        subCommands.put("test", new TestCommand());
        subCommands.put("stats", new StatsCommand());
        subCommands.put("maintenance", new MaintenanceCommand());
        subCommands.put("help", new HelpCommand());
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            return subCommands.get("help").execute(sender, args);
        }
        
        String subCommandName = args[0].toLowerCase();
        SubCommand subCommand = subCommands.get(subCommandName);
        
        if (subCommand == null) {
            MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") + 
                                   "&cUnknown subcommand: " + subCommandName);
            return subCommands.get("help").execute(sender, args);
        }
        
        // Check permission
        if (!subCommand.hasPermission(sender)) {
            MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") + 
                                   plugin.getConfig().getString("messages.no-permission"));
            return true;
        }
        
        // Execute subcommand
        String[] subArgs = Arrays.copyOfRange(args, 1, args.length);
        return subCommand.execute(sender, subArgs);
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (args.length == 1) {
            // Tab complete subcommands
            List<String> completions = new ArrayList<>();
            String partial = args[0].toLowerCase();
            
            for (String subCmd : subCommands.keySet()) {
                if (subCmd.startsWith(partial) && subCommands.get(subCmd).hasPermission(sender)) {
                    completions.add(subCmd);
                }
            }
            
            return completions;
        } else if (args.length > 1) {
            // Tab complete subcommand arguments
            SubCommand subCommand = subCommands.get(args[0].toLowerCase());
            if (subCommand != null && subCommand.hasPermission(sender)) {
                String[] subArgs = Arrays.copyOfRange(args, 1, args.length);
                return subCommand.tabComplete(sender, subArgs);
            }
        }
        
        return new ArrayList<>();
    }
    
    // Abstract base class for subcommands
    private abstract class SubCommand {
        public abstract boolean execute(CommandSender sender, String[] args);
        public abstract String getPermission();
        public abstract String getUsage();
        public abstract String getDescription();
        
        public boolean hasPermission(CommandSender sender) {
            return sender.hasPermission(getPermission());
        }
        
        public List<String> tabComplete(CommandSender sender, String[] args) {
            return new ArrayList<>();
        }
        
        protected boolean requirePlayer(CommandSender sender) {
            if (!(sender instanceof Player)) {
                MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") + 
                                       plugin.getConfig().getString("messages.player-only"));
                return false;
            }
            return true;
        }
    }
    
    // Create subcommand
    private class CreateCommand extends SubCommand {
        @Override
        public boolean execute(CommandSender sender, String[] args) {
            if (!requirePlayer(sender)) return true;

            Player player = (Player) sender;

            // Check if player creation is allowed
            if (!plugin.getConfig().getBoolean("shopkeeper.allow-player-creation", true)) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cPlayer shopkeeper creation is disabled!");
                return true;
            }

            // Check permission if required
            if (plugin.getConfig().getBoolean("shopkeeper.require-permission", true)) {
                if (!player.hasPermission("soulshopkeeper.create")) {
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                           plugin.getConfig().getString("messages.no-permission"));
                    return true;
                }
            }
            
            // Check if player has reached max shopkeepers
            int maxShopkeepers = plugin.getConfig().getInt("shopkeeper.max-per-player", -1);
            if (maxShopkeepers > 0) { // Only check limit if it's not unlimited (-1)
                int currentCount = plugin.getDataManager().getPlayerShopkeeperCount(player.getUniqueId());

                if (currentCount >= maxShopkeepers) {
                    String message = plugin.getConfig().getString("messages.max-shopkeepers-reached",
                                   "&cYou have reached the maximum number of shopkeepers!");
                    message = message.replace("{limit}", String.valueOf(maxShopkeepers));
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + message);
                    return true;
                }
            }
            
            // Create villager shopkeeper directly
            createVillagerShopkeeper(player);
            return true;
        }
        
        @Override
        public String getPermission() {
            return "soulshopkeeper.create";
        }
        
        @Override
        public String getUsage() {
            return "/soulshopkeeper create";
        }
        
        @Override
        public String getDescription() {
            return "Create a new shopkeeper";
        }

        /**
         * Creates a villager shopkeeper directly at player's location
         */
        private void createVillagerShopkeeper(Player player) {
            try {
                // Create shopkeeper with villager type
                SoulShopkeeper shopkeeper = new SoulShopkeeper(
                    java.util.UUID.randomUUID(),
                    player.getName() + "'s Shopkeeper",
                    "VILLAGER",
                    player.getLocation(),
                    player.getUniqueId()
                );

                // Save shopkeeper
                plugin.getDataManager().addShopkeeper(shopkeeper);

                // Spawn entity
                plugin.getEntityManager().spawnShopkeeperEntity(shopkeeper);

                // Send success message
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       plugin.getConfig().getString("messages.shopkeeper-created"));
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&7Use &e/soulshopkeeper edit &7or &eShift+Right-click &7to edit your shopkeeper!");
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&7Use &e/soulshopkeeper model <id> &7to change the shopkeeper's appearance!");

            } catch (Exception e) {
                plugin.getLogger().severe("Failed to create shopkeeper: " + e.getMessage());
                e.printStackTrace();
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cFailed to create shopkeeper! Check console for errors.");
            }
        }
    }

    // Edit subcommand
    private class EditCommand extends SubCommand {
        @Override
        public boolean execute(CommandSender sender, String[] args) {
            if (!requirePlayer(sender)) return true;
            
            Player player = (Player) sender;
            
            if (args.length == 0) {
                // Edit nearest shopkeeper
                SoulShopkeeper nearest = findNearestShopkeeper(player);
                if (nearest == null) {
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + 
                                           plugin.getConfig().getString("messages.shopkeeper-not-found"));
                    return true;
                }
                
                plugin.getGUIManager().openEditGUI(player, nearest);
            } else {
                // Edit specific shopkeeper by ID
                try {
                    String idInput = args[0];
                    UUID shopkeeperId;

                    // Try to parse as full UUID first
                    try {
                        shopkeeperId = UUID.fromString(idInput);
                    } catch (IllegalArgumentException e) {
                        // If not full UUID, try to find by partial ID
                        shopkeeperId = findShopkeeperByPartialId(player, idInput);
                        if (shopkeeperId == null) {
                            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                                   "&cShopkeeper not found! Use full UUID or first 8 characters.");
                            return true;
                        }
                    }

                    SoulShopkeeper shopkeeper = plugin.getDataManager().getShopkeeper(shopkeeperId);

                    if (shopkeeper == null) {
                        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                               plugin.getConfig().getString("messages.shopkeeper-not-found"));
                        return true;
                    }

                    // Check ownership
                    if (!shopkeeper.isOwnedBy(player.getUniqueId()) && !player.hasPermission("soulshopkeeper.admin")) {
                        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                               plugin.getConfig().getString("messages.no-permission"));
                        return true;
                    }

                    plugin.getGUIManager().openEditGUI(player, shopkeeper);
                } catch (Exception e) {
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                           "&cError finding shopkeeper: " + e.getMessage());
                    plugin.getLogger().warning("Error in edit command: " + e.getMessage());
                    return true;
                }
            }
            
            return true;
        }
        
        @Override
        public String getPermission() {
            return "soulshopkeeper.edit";
        }
        
        @Override
        public String getUsage() {
            return "/soulshopkeeper edit [id]";
        }
        
        @Override
        public String getDescription() {
            return "Edit a shopkeeper";
        }
    }
    
    // Delete subcommand
    private class DeleteCommand extends SubCommand {
        @Override
        public boolean execute(CommandSender sender, String[] args) {
            if (!requirePlayer(sender)) return true;
            
            Player player = (Player) sender;
            
            if (args.length == 0) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + 
                                       "&cUsage: " + getUsage());
                return true;
            }
            
            try {
                String idInput = args[0];
                UUID shopkeeperId;

                // Try to parse as full UUID first
                try {
                    shopkeeperId = UUID.fromString(idInput);
                } catch (IllegalArgumentException e) {
                    // If not full UUID, try to find by partial ID
                    shopkeeperId = findShopkeeperByPartialId(player, idInput);
                    if (shopkeeperId == null) {
                        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                               "&cShopkeeper not found! Use full UUID or first 8 characters.");
                        return true;
                    }
                }

                SoulShopkeeper shopkeeper = plugin.getDataManager().getShopkeeper(shopkeeperId);
                
                if (shopkeeper == null) {
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + 
                                           plugin.getConfig().getString("messages.shopkeeper-not-found"));
                    return true;
                }
                
                // Check ownership
                if (!shopkeeper.isOwnedBy(player.getUniqueId()) && !player.hasPermission("soulshopkeeper.admin")) {
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + 
                                           plugin.getConfig().getString("messages.no-permission"));
                    return true;
                }
                
                try {
                    // Remove entity first
                    plugin.getEntityManager().removeShopkeeperEntity(shopkeeperId);

                    // Delete shopkeeper from data
                    boolean deleted = false;
                    if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                        shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                            (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                        deleted = wrapper.getSQLiteDataManager().deleteShopkeeper(shopkeeperId);
                    } else {
                        plugin.getDataManager().removeShopkeeper(shopkeeperId);
                        deleted = true;
                    }

                    if (deleted) {
                        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                               plugin.getConfig().getString("messages.shopkeeper-deleted"));
                    } else {
                        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                               "&cFailed to delete shopkeeper from database!");
                    }
                } catch (Exception e) {
                    MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                           "&cError deleting shopkeeper: " + e.getMessage());
                    plugin.getLogger().severe("Failed to delete shopkeeper " + shopkeeperId + ": " + e.getMessage());
                    e.printStackTrace();
                }
                
            } catch (IllegalArgumentException e) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + 
                                       "&cInvalid shopkeeper ID!");
            }
            
            return true;
        }
        
        @Override
        public String getPermission() {
            return "soulshopkeeper.delete";
        }
        
        @Override
        public String getUsage() {
            return "/soulshopkeeper delete <id>";
        }
        
        @Override
        public String getDescription() {
            return "Delete a shopkeeper";
        }
    }
    
    // List subcommand
    private class ListCommand extends SubCommand {
        @Override
        public boolean execute(CommandSender sender, String[] args) {
            if (!requirePlayer(sender)) return true;
            
            Player player = (Player) sender;
            Collection<SoulShopkeeper> shopkeepers = plugin.getDataManager().getPlayerShopkeepers(player.getUniqueId());
            
            if (shopkeepers.isEmpty()) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + 
                                       "&cYou don't have any shopkeepers!");
                return true;
            }
            
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + 
                                   "&6Your Shopkeepers:");
            
            for (SoulShopkeeper shopkeeper : shopkeepers) {
                String status = shopkeeper.isActive() ? "&a✓" : "&c✗";
                MessageUtils.sendMessage(player, String.format("&7- %s &f%s &7(%s) - %d trades", 
                                       status, shopkeeper.getName(), shopkeeper.getId().toString().substring(0, 8), 
                                       shopkeeper.getTradeCount()));
            }
            
            return true;
        }
        
        @Override
        public String getPermission() {
            return "soulshopkeeper.use";
        }
        
        @Override
        public String getUsage() {
            return "/soulshopkeeper list";
        }
        
        @Override
        public String getDescription() {
            return "List your shopkeepers";
        }
    }
    
    // Info subcommand
    private class InfoCommand extends SubCommand {
        @Override
        public boolean execute(CommandSender sender, String[] args) {
            if (args.length == 0) {
                MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") + 
                                       "&cUsage: " + getUsage());
                return true;
            }
            
            try {
                UUID shopkeeperId = UUID.fromString(args[0]);
                SoulShopkeeper shopkeeper = plugin.getDataManager().getShopkeeper(shopkeeperId);
                
                if (shopkeeper == null) {
                    MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") + 
                                           plugin.getConfig().getString("messages.shopkeeper-not-found"));
                    return true;
                }
                
                // Display shopkeeper info
                MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") + 
                                       "&6Shopkeeper Info:");
                MessageUtils.sendMessage(sender, "&7Name: &f" + shopkeeper.getName());
                MessageUtils.sendMessage(sender, "&7Type: &f" + shopkeeper.getType());
                MessageUtils.sendMessage(sender, "&7ID: &f" + shopkeeper.getId());
                MessageUtils.sendMessage(sender, "&7Active: " + (shopkeeper.isActive() ? "&aYes" : "&cNo"));
                MessageUtils.sendMessage(sender, "&7Trades: &f" + shopkeeper.getTradeCount());
                MessageUtils.sendMessage(sender, "&7Location: &f" + formatLocation(shopkeeper.getLocation()));
                
                if (shopkeeper.hasOwner()) {
                    MessageUtils.sendMessage(sender, "&7Owner: &f" + shopkeeper.getOwnerId());
                }
                
            } catch (IllegalArgumentException e) {
                MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") + 
                                       "&cInvalid shopkeeper ID!");
            }
            
            return true;
        }
        
        @Override
        public String getPermission() {
            return "soulshopkeeper.use";
        }
        
        @Override
        public String getUsage() {
            return "/soulshopkeeper info <id>";
        }
        
        @Override
        public String getDescription() {
            return "Get information about a shopkeeper";
        }
    }

    // Model subcommand
    private class ModelCommand extends SubCommand {
        @Override
        public boolean execute(CommandSender sender, String[] args) {
            if (!requirePlayer(sender)) return true;

            Player player = (Player) sender;

            if (args.length == 0) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cUsage: " + getUsage());
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&7Available models:");
                MessageUtils.sendMessage(player, "&e• Basic: &fVILLAGER, ZOMBIE, SKELETON, CREEPER, WITCH");
                MessageUtils.sendMessage(player, "&e• Large: &fIRON_GOLEM, WITHER, ENDER_DRAGON");
                MessageUtils.sendMessage(player, "&e• Animals: &fCOW, PIG, SHEEP, CHICKEN, HORSE");
                MessageUtils.sendMessage(player, "&e• Hostile: &fENDERMAN, BLAZE, GHAST, SPIDER");
                return true;
            }

            String modelType = args[0].toUpperCase();

            // Validate model type
            if (!isValidModelType(modelType)) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cInvalid model type! Use &e/soulshopkeeper model &cto see available models.");
                return true;
            }

            // Find nearest shopkeeper owned by player
            SoulShopkeeper nearest = findNearestOwnedShopkeeper(player);

            if (nearest == null) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cNo shopkeeper found nearby! Stand within 5 blocks of your shopkeeper.");
                return true;
            }

            // Change model
            changeShopkeeperModel(player, nearest, modelType);
            return true;
        }

        private boolean isValidModelType(String type) {
            // Basic models
            if (type.equals("VILLAGER") || type.equals("ZOMBIE") || type.equals("SKELETON") ||
                type.equals("CREEPER") || type.equals("WITCH")) {
                return true;
            }

            // Large models
            if (type.equals("IRON_GOLEM") || type.equals("WITHER") || type.equals("ENDER_DRAGON")) {
                return true;
            }

            // Animal models
            if (type.equals("COW") || type.equals("PIG") || type.equals("SHEEP") ||
                type.equals("CHICKEN") || type.equals("HORSE")) {
                return true;
            }

            // Hostile models
            if (type.equals("ENDERMAN") || type.equals("BLAZE") || type.equals("GHAST") ||
                type.equals("SPIDER")) {
                return true;
            }

            return false;
        }

        private SoulShopkeeper findNearestOwnedShopkeeper(Player player) {
            Collection<SoulShopkeeper> playerShopkeepers = plugin.getDataManager().getPlayerShopkeepers(player.getUniqueId());
            SoulShopkeeper nearest = null;
            double minDistance = 5.0; // 5 block radius

            for (SoulShopkeeper shopkeeper : playerShopkeepers) {
                if (shopkeeper.getLocation().getWorld().equals(player.getWorld())) {
                    double distance = shopkeeper.getLocation().distance(player.getLocation());
                    if (distance <= minDistance) {
                        minDistance = distance;
                        nearest = shopkeeper;
                    }
                }
            }

            return nearest;
        }

        private void changeShopkeeperModel(Player player, SoulShopkeeper shopkeeper, String newType) {
            try {
                String oldType = shopkeeper.getType();

                // Update shopkeeper type
                shopkeeper.setType(newType);

                // Save changes
                if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                    shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                        (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                    wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
                } else {
                    plugin.getDataManager().saveAll();
                }

                // Remove old entity and spawn new one
                plugin.getEntityManager().removeShopkeeperEntity(shopkeeper.getId());
                plugin.getEntityManager().spawnShopkeeperEntity(shopkeeper);

                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&aShopkeeper model changed from &e" + oldType + " &ato &e" + newType + "&a!");

            } catch (Exception e) {
                plugin.getLogger().severe("Failed to change shopkeeper model: " + e.getMessage());
                e.printStackTrace();
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cFailed to change model! Check console for errors.");
            }
        }

        @Override
        public String getPermission() {
            return "soulshopkeeper.model";
        }

        @Override
        public String getUsage() {
            return "/soulshopkeeper model <type>";
        }

        @Override
        public String getDescription() {
            return "Change shopkeeper model/appearance";
        }
    }

    // Reload subcommand
    private class ReloadCommand extends SubCommand {
        @Override
        public boolean execute(CommandSender sender, String[] args) {
            MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") +
                                   "&eReloading SoulShopkeeper...");

            try {
                // Save all data first
                plugin.getDataManager().saveAll();
                if (plugin.getSQLiteDataManager() != null) {
                    plugin.getSQLiteDataManager().saveAll();
                }

                // Reload config
                plugin.reloadConfig();
                plugin.getMMOItemStorage().clearCache();

                // Use safe reload method to prevent duplicates
                plugin.getEntityManager().reloadAllShopkeepers();

                // Send completion message after delay
                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") +
                                           "&aReload completed! All shopkeepers safely respawned.");
                }, 40L); // 2 second delay to ensure completion

                MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") +
                                       "&aConfiguration reloaded! Respawning entities...");

            } catch (Exception e) {
                plugin.getLogger().severe("Error during reload: " + e.getMessage());
                e.printStackTrace();
                MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") +
                                       "&cReload failed! Check console for errors.");
            }

            return true;
        }
        
        @Override
        public String getPermission() {
            return "soulshopkeeper.admin";
        }
        
        @Override
        public String getUsage() {
            return "/soulshopkeeper reload";
        }
        
        @Override
        public String getDescription() {
            return "Reload the plugin configuration";
        }
    }

    // Cleanup subcommand
    private class CleanupCommand extends SubCommand {
        @Override
        public boolean execute(CommandSender sender, String[] args) {
            MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") +
                                   "&eStarting cleanup of duplicate shopkeeper entities...");

            try {
                // Force cleanup all worlds
                plugin.getEntityManager().reloadAllShopkeepers();

                MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") +
                                       "&aCleanup initiated! Check console for details.");

                // Send completion message after delay
                Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") +
                                           "&aCleanup completed! All duplicate entities removed.");
                }, 40L);

            } catch (Exception e) {
                plugin.getLogger().severe("Error during cleanup: " + e.getMessage());
                e.printStackTrace();
                MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") +
                                       "&cCleanup failed! Check console for errors.");
            }

            return true;
        }

        @Override
        public String getPermission() {
            return "soulshopkeeper.admin";
        }

        @Override
        public String getUsage() {
            return "/soulshopkeeper cleanup";
        }

        @Override
        public String getDescription() {
            return "Clean up duplicate shopkeeper entities";
        }
    }

    // Save subcommand
    private class SaveCommand extends SubCommand {
        @Override
        public boolean execute(CommandSender sender, String[] args) {
            plugin.getDataManager().saveAll();
            
            MessageUtils.sendMessage(sender, plugin.getConfig().getString("messages.prefix") + 
                                   "&aAll data saved!");
            return true;
        }
        
        @Override
        public String getPermission() {
            return "soulshopkeeper.admin";
        }
        
        @Override
        public String getUsage() {
            return "/soulshopkeeper save";
        }
        
        @Override
        public String getDescription() {
            return "Save all shopkeeper data";
        }
    }
    
    // Test subcommand
    private class TestCommand extends SubCommand {
        @Override
        public boolean execute(CommandSender sender, String[] args) {
            DatabaseTestUtils.testDatabaseStorage(plugin, sender);
            return true;
        }

        @Override
        public String getPermission() {
            return "soulshopkeeper.admin";
        }

        @Override
        public String getUsage() {
            return "/soulshopkeeper test";
        }

        @Override
        public String getDescription() {
            return "Test database storage functionality";
        }
    }

    // Stats subcommand
    private class StatsCommand extends SubCommand {
        @Override
        public boolean execute(CommandSender sender, String[] args) {
            DatabaseTestUtils.showDatabaseStats(plugin, sender);
            return true;
        }

        @Override
        public String getPermission() {
            return "soulshopkeeper.admin";
        }

        @Override
        public String getUsage() {
            return "/soulshopkeeper stats";
        }

        @Override
        public String getDescription() {
            return "Show database statistics";
        }
    }

    // Maintenance subcommand
    private class MaintenanceCommand extends SubCommand {
        @Override
        public boolean execute(CommandSender sender, String[] args) {
            DatabaseTestUtils.performDatabaseMaintenance(plugin, sender);
            return true;
        }

        @Override
        public String getPermission() {
            return "soulshopkeeper.admin";
        }

        @Override
        public String getUsage() {
            return "/soulshopkeeper maintenance";
        }

        @Override
        public String getDescription() {
            return "Perform database maintenance";
        }
    }

    // Help subcommand
    private class HelpCommand extends SubCommand {
        @Override
        public boolean execute(CommandSender sender, String[] args) {
            MessageUtils.sendMessage(sender, "&6&l=== SoulShopkeeper Help ===");

            for (Map.Entry<String, SubCommand> entry : subCommands.entrySet()) {
                SubCommand cmd = entry.getValue();
                if (cmd.hasPermission(sender)) {
                    MessageUtils.sendMessage(sender, String.format("&e%s &7- %s",
                                           cmd.getUsage(), cmd.getDescription()));
                }
            }

            return true;
        }

        @Override
        public String getPermission() {
            return "soulshopkeeper.use";
        }

        @Override
        public String getUsage() {
            return "/soulshopkeeper help";
        }

        @Override
        public String getDescription() {
            return "Show this help message";
        }
    }
    
    // Utility methods
    private SoulShopkeeper findNearestShopkeeper(Player player) {
        double minDistance = Double.MAX_VALUE;
        SoulShopkeeper nearest = null;

        for (SoulShopkeeper shopkeeper : plugin.getDataManager().getAllShopkeepers()) {
            if (shopkeeper.getLocation().getWorld().equals(player.getWorld())) {
                double distance = shopkeeper.getLocation().distance(player.getLocation());
                if (distance < minDistance && distance <= 10.0) { // Within 10 blocks
                    minDistance = distance;
                    nearest = shopkeeper;
                }
            }
        }

        return nearest;
    }

    /**
     * Finds a shopkeeper by partial ID (first 8 characters)
     */
    private UUID findShopkeeperByPartialId(Player player, String partialId) {
        if (partialId.length() < 8) {
            return null;
        }

        String searchId = partialId.toLowerCase();

        // First, search in player's own shopkeepers
        for (SoulShopkeeper shopkeeper : plugin.getDataManager().getPlayerShopkeepers(player.getUniqueId())) {
            String fullId = shopkeeper.getId().toString().toLowerCase();
            if (fullId.startsWith(searchId)) {
                return shopkeeper.getId();
            }
        }

        // If admin, search in all shopkeepers
        if (player.hasPermission("soulshopkeeper.admin")) {
            for (SoulShopkeeper shopkeeper : plugin.getDataManager().getAllShopkeepers()) {
                String fullId = shopkeeper.getId().toString().toLowerCase();
                if (fullId.startsWith(searchId)) {
                    return shopkeeper.getId();
                }
            }
        }

        return null;
    }
    
    private String formatLocation(org.bukkit.Location location) {
        return String.format("%s: %.1f, %.1f, %.1f", 
                           location.getWorld().getName(), 
                           location.getX(), 
                           location.getY(), 
                           location.getZ());
    }
}
