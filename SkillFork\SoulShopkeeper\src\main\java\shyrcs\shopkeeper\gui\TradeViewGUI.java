package shyrcs.shopkeeper.gui;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

import java.util.Arrays;
import java.util.List;

/**
 * GUI for viewing trade recipes in a crafting-style layout
 */
public class TradeViewGUI {
    
    private final SoulShopkeeperPlugin plugin;
    
    public TradeViewGUI(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Opens the trade view GUI for a specific trade
     */
    public void openTradeViewGUI(Player player, SoulShopkeeper shopkeeper, int tradeIndex) {
        if (tradeIndex < 0 || tradeIndex >= shopkeeper.getTrades().size()) {
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") + 
                                   "&cInvalid trade index!");
            return;
        }
        
        ShopkeeperTrade trade = shopkeeper.getTrades().get(tradeIndex);
        String title = MessageUtils.colorize("&6&lTrade Recipe #" + (tradeIndex + 1));
        Inventory gui = Bukkit.createInventory(null, 45, title);
        
        setupTradeViewGUI(gui, trade);
        
        // Create session for this GUI
        plugin.getGUIManager().createTradeViewSession(player, shopkeeper, tradeIndex);
        
        player.openInventory(gui);
    }
    
    /**
     * Sets up the trade view GUI layout
     */
    private void setupTradeViewGUI(Inventory gui, ShopkeeperTrade trade) {
        // Fill with white glass panes (inside)
        ItemStack whiteGlass = createGlassPane(Material.WHITE_STAINED_GLASS_PANE, " ");
        for (int i = 0; i < 45; i++) {
            gui.setItem(i, whiteGlass);
        }
        
        // Gray glass border (top and bottom rows)
        ItemStack grayGlass = createGlassPane(Material.GRAY_STAINED_GLASS_PANE, " ");
        for (int i = 0; i < 9; i++) {
            gui.setItem(i, grayGlass); // Top row
            gui.setItem(36 + i, grayGlass); // Bottom row
        }
        
        // Gray glass sides
        for (int i = 9; i < 36; i += 9) {
            gui.setItem(i, grayGlass); // Left side
            gui.setItem(i + 8, grayGlass); // Right side
        }
        
        // Yellow glass decorations
        ItemStack yellowGlass = createGlassPane(Material.YELLOW_STAINED_GLASS_PANE, "&e&lCrafting Recipe");
        gui.setItem(15, yellowGlass);
        gui.setItem(23, yellowGlass);
        gui.setItem(25, yellowGlass);
        gui.setItem(33, yellowGlass);
        
        // Recipe slots (ingredients) - 3x3 grid
        // Slots: 10,11,12,19,20,21,28,29,30
        ItemStack ingredient1 = trade.getIngredient1Item();
        ItemStack ingredient2 = trade.getIngredient2Item();
        
        // Place ingredients in crafting grid
        if (ingredient1 != null) {
            gui.setItem(10, ingredient1.clone()); // Top-left
        }
        if (ingredient2 != null) {
            gui.setItem(11, ingredient2.clone()); // Top-middle
        }
        
        // Result slot (slot 24)
        ItemStack result = trade.getResultItem();
        if (result != null) {
            ItemStack resultDisplay = result.clone();
            ItemMeta meta = resultDisplay.getItemMeta();
            if (meta != null) {
                List<String> lore = meta.getLore();
                if (lore == null) {
                    lore = Arrays.asList();
                }
                lore.add("");
                lore.add(MessageUtils.colorize("&e&lClick to Trade!"));
                lore.add(MessageUtils.colorize("&7This will open the trading interface"));
                meta.setLore(lore);
                resultDisplay.setItemMeta(meta);
            }
            gui.setItem(24, resultDisplay);
        }
        
        // Back button
        gui.setItem(4, createItem(Material.BARRIER, "&c&lBack", 
                                 "&7Return to shopkeeper menu"));
    }
    
    /**
     * Creates a glass pane item
     */
    private ItemStack createGlassPane(Material material, String name) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(MessageUtils.colorize(name));
            item.setItemMeta(meta);
        }
        return item;
    }
    
    /**
     * Creates an item with display name and lore
     */
    private ItemStack createItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(MessageUtils.colorize(name));
            if (lore.length > 0) {
                meta.setLore(Arrays.asList(lore).stream()
                    .map(MessageUtils::colorize)
                    .collect(java.util.stream.Collectors.toList()));
            }
            item.setItemMeta(meta);
        }
        return item;
    }
}
