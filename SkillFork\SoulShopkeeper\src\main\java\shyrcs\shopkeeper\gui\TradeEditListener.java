package shyrcs.shopkeeper.gui;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.ItemStack;
import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.enums.TradeMode;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

/**
 * Listener for GUI-based trade editing
 */
public class TradeEditListener implements Listener {
    
    private final SoulShopkeeperPlugin plugin;
    private final TradeEditManager tradeEditManager;
    
    public TradeEditListener(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
        this.tradeEditManager = new TradeEditManager(plugin);
    }
    
    @EventHandler(priority = EventPriority.NORMAL)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();
        
        // Handle GUI Trade Edit
        if (title.contains("Edit Trade #") && title.contains("(GUI Mode)")) {
            handleGUITradeEditClick(event, player);
            return;
        }
    }
    
    /**
     * Handles clicks in GUI trade edit interface
     */
    private void handleGUITradeEditClick(InventoryClickEvent event, Player player) {
        int slot = event.getSlot();
        
        // Get session
        ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
        if (session == null || session.getType() != ShopkeeperGUIManager.GUIType.TRADE_EDIT) {
            event.setCancelled(true);
            player.closeInventory();
            return;
        }
        
        SoulShopkeeper shopkeeper = session.getShopkeeper();
        int tradeIndex = session.getTradeIndex();
        
        if (shopkeeper == null || tradeIndex < 0) {
            event.setCancelled(true);
            player.closeInventory();
            return;
        }
        
        // Allow item placement in crafting slots and result slot
        int[] allowedSlots = {19, 20, 21, 28, 29, 30, 37, 38, 39, 24}; // Crafting grid + result
        boolean isAllowedSlot = false;
        for (int allowedSlot : allowedSlots) {
            if (slot == allowedSlot) {
                isAllowedSlot = true;
                break;
            }
        }
        
        if (isAllowedSlot) {
            // Allow item manipulation in crafting area
            return;
        }
        
        // Cancel clicks on control buttons and handle them
        event.setCancelled(true);
        
        switch (slot) {
            case 45: // Save Trade
                tradeEditManager.saveGUITrade(player, shopkeeper, tradeIndex, event.getInventory());
                // Refresh GUI
                org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    tradeEditManager.openTradeEdit(player, shopkeeper, tradeIndex);
                }, 1L);
                break;
                
            case 46: // Clear Trade
                clearTradeItems(event.getInventory());
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&eTrade items cleared.");
                break;
                
            case 47: // Toggle Enable/Disable
                toggleTradeEnabled(shopkeeper, tradeIndex, player);
                // Refresh GUI
                org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    tradeEditManager.openTradeEdit(player, shopkeeper, tradeIndex);
                }, 1L);
                break;
                
            case 49: // Switch to Native Mode
                shopkeeper.setTradeMode(TradeMode.NATIVE);
                saveShopkeeper(shopkeeper);
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&eSwitched to Native Trading mode.");
                player.closeInventory();
                // Open native trade edit
                org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    plugin.getGUIManager().openTradeEditGUI(player, shopkeeper, tradeIndex);
                }, 1L);
                break;
                
            case 53: // Back
                player.closeInventory();
                plugin.getGUIManager().openEditGUI(player, shopkeeper);
                break;
                
            default:
                // Do nothing for other slots
                break;
        }
    }
    
    /**
     * Clears trade items from crafting area
     */
    private void clearTradeItems(org.bukkit.inventory.Inventory gui) {
        int[] craftingSlots = {19, 20, 21, 28, 29, 30, 37, 38, 39, 24};
        for (int slot : craftingSlots) {
            gui.setItem(slot, new ItemStack(Material.WHITE_STAINED_GLASS_PANE));
        }
    }
    
    /**
     * Toggles trade enabled status
     */
    private void toggleTradeEnabled(SoulShopkeeper shopkeeper, int tradeIndex, Player player) {
        if (tradeIndex < 0 || tradeIndex >= shopkeeper.getTrades().size()) {
            return;
        }
        
        ShopkeeperTrade trade = shopkeeper.getTrades().get(tradeIndex);
        trade.setEnabled(!trade.isEnabled());
        
        saveShopkeeper(shopkeeper);
        
        MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                               "&aTrade " + (tradeIndex + 1) + " " +
                               (trade.isEnabled() ? "enabled" : "disabled"));
    }
    
    /**
     * Saves shopkeeper to database
     */
    private void saveShopkeeper(SoulShopkeeper shopkeeper) {
        try {
            if (plugin.getDataManager() instanceof shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) {
                shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper wrapper =
                    (shyrcs.shopkeeper.storage.database.DatabaseDataManagerWrapper) plugin.getDataManager();
                wrapper.getSQLiteDataManager().saveShopkeeper(shopkeeper);
                wrapper.saveAll();
            } else {
                plugin.getDataManager().saveAll();
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to save shopkeeper: " + e.getMessage());
        }
    }
    
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getPlayer();
        String title = event.getView().getTitle();
        
        // Handle GUI trade edit close
        if (title.contains("Edit Trade #") && title.contains("(GUI Mode)")) {
            // Auto-save on close
            ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
            if (session != null && session.getType() == ShopkeeperGUIManager.GUIType.TRADE_EDIT) {
                SoulShopkeeper shopkeeper = session.getShopkeeper();
                int tradeIndex = session.getTradeIndex();
                
                if (shopkeeper != null && tradeIndex >= 0) {
                    // Auto-save current state
                    tradeEditManager.saveGUITrade(player, shopkeeper, tradeIndex, event.getInventory());
                }
                
                // Clean up session
                org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                    plugin.getGUIManager().removeSession(player.getUniqueId());
                }, 1L);
            }
        }
    }
}
