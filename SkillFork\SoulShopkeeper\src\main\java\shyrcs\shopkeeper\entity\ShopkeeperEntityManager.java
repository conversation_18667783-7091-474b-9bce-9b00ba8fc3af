package shyrcs.shopkeeper.entity;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.entity.Villager;
import org.bukkit.metadata.FixedMetadataValue;

import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * Manages shopkeeper entities independently without external dependencies
 */
public class ShopkeeperEntityManager {
    
    private final SoulShopkeeperPlugin plugin;
    private final Logger logger;
    
    // Map to track spawned entities
    private final Map<UUID, Entity> spawnedEntities;
    
    // Metadata key for identifying shopkeeper entities
    private static final String METADATA_KEY = "soulshopkeeper_id";
    
    public ShopkeeperEntityManager(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.spawnedEntities = new ConcurrentHashMap<>();
    }
    
    /**
     * Spawns an entity for a shopkeeper
     */
    public Entity spawnShopkeeperEntity(SoulShopkeeper shopkeeper) {
        try {
            Location location = shopkeeper.getLocation();
            EntityType entityType = shopkeeper.getEntityType();
            
            // Spawn the entity
            Entity entity = location.getWorld().spawnEntity(location, entityType);
            
            if (entity instanceof LivingEntity) {
                LivingEntity livingEntity = (LivingEntity) entity;
                
                // Configure entity properties
                configureEntity(livingEntity, shopkeeper);
                
                // Add metadata to identify this as a shopkeeper
                entity.setMetadata(METADATA_KEY, new FixedMetadataValue(plugin, shopkeeper.getId().toString()));
                
                // Store entity reference
                spawnedEntities.put(shopkeeper.getId(), entity);
                shopkeeper.setEntity(entity);
                
                if (plugin.getConfig().getBoolean("general.debug", false)) {
                    logger.info("Spawned shopkeeper entity: " + entityType + " at " + formatLocation(location));
                }
                
                return entity;
            }
            
        } catch (Exception e) {
            logger.severe("Failed to spawn shopkeeper entity: " + e.getMessage());
            e.printStackTrace();
        }
        
        return null;
    }
    
    /**
     * Configures entity properties
     */
    private void configureEntity(LivingEntity entity, SoulShopkeeper shopkeeper) {
        // Prevent entity from despawning
        entity.setRemoveWhenFarAway(false);
        entity.setPersistent(true);

        // Keep AI enabled but remove unwanted behaviors
        entity.setAI(true);
        entity.setGravity(false); // No gravity - cannot be pushed
        entity.setSilent(true); // No sounds

        // Remove AI goals to prevent movement but keep head tracking
        removeUnwantedAIGoals(entity);

        // Make entity completely immobile
        makeEntityImmobile(entity);

        // Make shopkeepers completely immortal - no damage from any source
        entity.setInvulnerable(true);
        entity.setHealth(entity.getMaxHealth()); // Full health
        entity.setFireTicks(0); // No fire damage

        // Set custom name
        entity.setCustomName(shopkeeper.getName());
        entity.setCustomNameVisible(true);

        // Configure specific entity types
        if (entity instanceof Villager) {
            Villager villager = (Villager) entity;
            villager.setProfession(Villager.Profession.NONE);
            villager.setVillagerType(Villager.Type.PLAINS);
            villager.setVillagerLevel(1);

            // Prevent trading with vanilla mechanics
            villager.setRecipes(java.util.Collections.emptyList());

            // Disable villager-specific behaviors
            villager.setBreed(false);
        }

        // Configure other entity types to be completely static
        configureEntityTypeSpecific(entity);

        // Only start looking behavior if enabled in config
        if (plugin.getConfig().getBoolean("entity.look-at-players", true)) {
            startSmoothLookAtPlayerBehavior(entity);
        }

        // Start position monitoring to prevent movement
        startPositionMonitoring(entity, shopkeeper);

        // Add more entity type configurations as needed
    }
    
    /**
     * Removes a shopkeeper entity
     */
    public void removeShopkeeperEntity(UUID shopkeeperId) {
        Entity entity = spawnedEntities.remove(shopkeeperId);
        if (entity != null && !entity.isDead()) {
            entity.remove();
            
            if (plugin.getConfig().getBoolean("general.debug", false)) {
                logger.info("Removed shopkeeper entity: " + shopkeeperId.toString().substring(0, 8));
            }
        }
    }
    
    /**
     * Removes a shopkeeper entity by entity reference
     */
    public void removeShopkeeperEntity(Entity entity) {
        if (entity != null && entity.hasMetadata(METADATA_KEY)) {
            String shopkeeperIdString = entity.getMetadata(METADATA_KEY).get(0).asString();
            try {
                UUID shopkeeperId = UUID.fromString(shopkeeperIdString);
                removeShopkeeperEntity(shopkeeperId);
            } catch (IllegalArgumentException e) {
                // Invalid UUID, just remove the entity
                entity.remove();
            }
        }
    }
    
    /**
     * Checks if an entity is a shopkeeper
     */
    public boolean isShopkeeperEntity(Entity entity) {
        if (entity == null || entity.isDead()) {
            return false;
        }

        // Check for our metadata
        if (entity.hasMetadata(METADATA_KEY)) {
            return true;
        }

        // Also check for alternative metadata key (in case of inconsistency)
        if (entity.hasMetadata("soulshopkeeper_id")) {
            return true;
        }

        // Check if entity is in our tracking map
        return spawnedEntities.containsValue(entity);
    }
    
    /**
     * Gets the shopkeeper ID from an entity
     */
    public UUID getShopkeeperId(Entity entity) {
        if (!isShopkeeperEntity(entity)) {
            return null;
        }
        
        try {
            String idString = entity.getMetadata(METADATA_KEY).get(0).asString();
            return UUID.fromString(idString);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Gets the shopkeeper associated with an entity
     */
    public SoulShopkeeper getShopkeeper(Entity entity) {
        UUID shopkeeperId = getShopkeeperId(entity);
        if (shopkeeperId != null) {
            return plugin.getDataManager().getShopkeeper(shopkeeperId);
        }
        return null;
    }
    
    /**
     * Spawns all active shopkeepers
     */
    public void spawnAllShopkeepers() {
        logger.info("Starting to spawn all shopkeepers...");

        // First, cleanup any existing entities to prevent duplicates
        cleanupExistingEntities();

        int spawned = 0;
        int skipped = 0;

        for (SoulShopkeeper shopkeeper : plugin.getDataManager().getAllShopkeepers()) {
            if (shopkeeper.isActive()) {
                // Check if already spawned to prevent duplicates
                if (spawnedEntities.containsKey(shopkeeper.getId())) {
                    skipped++;
                    continue;
                }

                Entity entity = spawnShopkeeperEntity(shopkeeper);
                if (entity != null) {
                    spawned++;
                }
            }
        }

        logger.info("Spawned " + spawned + " shopkeeper entities (skipped " + skipped + " already spawned)");
    }

    /**
     * Safely reloads all shopkeeper entities (for plugin reload)
     */
    public void reloadAllShopkeepers() {
        logger.info("Starting safe reload of all shopkeeper entities...");

        // Step 1: Remove all existing entities
        removeAllShopkeeperEntities();

        // Step 2: Wait longer to ensure cleanup
        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
            // Step 3: Force cleanup any remaining entities
            logger.info("Performing force cleanup of remaining entities...");
            forceCleanupAllWorlds();

            // Step 4: Wait longer then spawn new entities
            org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                logger.info("Spawning fresh shopkeeper entities...");
                spawnAllShopkeepers();
                logger.info("Safe reload completed.");
            }, 5L); // Increased delay
        }, 3L); // Increased delay
    }

    /**
     * Cleanup existing entities before spawning new ones
     */
    private void cleanupExistingEntities() {
        try {
            int cleaned = 0;
            for (org.bukkit.World world : org.bukkit.Bukkit.getWorlds()) {
                for (org.bukkit.entity.Entity entity : new java.util.ArrayList<>(world.getEntities())) {
                    if (isShopkeeperEntity(entity)) {
                        // Check if this entity is tracked
                        boolean isTracked = spawnedEntities.containsValue(entity);
                        if (!isTracked) {
                            // Untracked shopkeeper entity - remove it
                            entity.remove();
                            cleaned++;
                        }
                    }
                }
            }
            if (cleaned > 0) {
                logger.info("Cleaned up " + cleaned + " untracked shopkeeper entities");
            }
        } catch (Exception e) {
            logger.warning("Error during entity cleanup: " + e.getMessage());
        }
    }
    
    /**
     * Removes all shopkeeper entities
     */
    public void removeAllShopkeeperEntities() {
        plugin.getLogger().info("Starting removal of all shopkeeper entities...");
        int removed = 0;

        // Create a copy of the keyset to avoid concurrent modification
        java.util.Set<UUID> shopkeeperIds = new java.util.HashSet<>(spawnedEntities.keySet());

        for (UUID shopkeeperId : shopkeeperIds) {
            try {
                removeShopkeeperEntity(shopkeeperId);
                removed++;
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to remove shopkeeper entity " + shopkeeperId + ": " + e.getMessage());
            }
        }

        // Double-check and force remove any remaining entities
        java.util.Set<org.bukkit.entity.Entity> remainingEntities = new java.util.HashSet<>(spawnedEntities.values());
        for (org.bukkit.entity.Entity entity : remainingEntities) {
            try {
                if (entity != null && !entity.isDead()) {
                    entity.remove();
                    removed++;
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to force remove entity: " + e.getMessage());
            }
        }

        logger.info("Removed " + removed + " shopkeeper entities");
    }
    
    /**
     * Refreshes a shopkeeper entity (remove and respawn)
     */
    public void refreshShopkeeperEntity(SoulShopkeeper shopkeeper) {
        removeShopkeeperEntity(shopkeeper.getId());
        
        if (shopkeeper.isActive()) {
            spawnShopkeeperEntity(shopkeeper);
        }
    }
    
    /**
     * Gets all spawned shopkeeper entities
     */
    public Map<UUID, Entity> getSpawnedEntities() {
        return new ConcurrentHashMap<>(spawnedEntities);
    }
    
    /**
     * Cleans up invalid entities
     */
    public void cleanupInvalidEntities() {
        int cleaned = 0;
        
        for (Map.Entry<UUID, Entity> entry : new java.util.HashSet<>(spawnedEntities.entrySet())) {
            Entity entity = entry.getValue();
            UUID shopkeeperId = entry.getKey();
            
            if (entity == null || entity.isDead()) {
                spawnedEntities.remove(shopkeeperId);
                cleaned++;
            } else {
                // Check if shopkeeper still exists
                SoulShopkeeper shopkeeper = plugin.getDataManager().getShopkeeper(shopkeeperId);
                if (shopkeeper == null) {
                    entity.remove();
                    spawnedEntities.remove(shopkeeperId);
                    cleaned++;
                }
            }
        }
        
        if (cleaned > 0) {
            logger.info("Cleaned up " + cleaned + " invalid shopkeeper entities");
        }
    }
    
    /**
     * Starts the entity cleanup task
     */
    public void startCleanupTask() {
        long interval = 20 * 60 * 5; // 5 minutes
        
        Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            cleanupInvalidEntities();
        }, interval, interval);
    }
    
    /**
     * Formats a location for logging
     */
    private String formatLocation(Location location) {
        return String.format("%s: %.1f, %.1f, %.1f", 
                           location.getWorld().getName(), 
                           location.getX(), 
                           location.getY(), 
                           location.getZ());
    }
    
    /**
     * Configures entity type specific behaviors
     */
    private void configureEntityTypeSpecific(LivingEntity entity) {
        // Disable specific behaviors for different entity types
        if (entity instanceof org.bukkit.entity.Zombie) {
            org.bukkit.entity.Zombie zombie = (org.bukkit.entity.Zombie) entity;
            // Prevent burning in sunlight
            zombie.setShouldBurnInDay(false);
            entity.setSilent(true);
        } else if (entity instanceof org.bukkit.entity.Skeleton) {
            org.bukkit.entity.Skeleton skeleton = (org.bukkit.entity.Skeleton) entity;
            // Prevent burning in sunlight
            skeleton.setShouldBurnInDay(false);
            // Remove bow from skeleton
            skeleton.getEquipment().setItemInMainHand(null);
            skeleton.getEquipment().setItemInOffHand(null);
            entity.setSilent(true);
        } else if (entity instanceof org.bukkit.entity.Creeper) {
            org.bukkit.entity.Creeper creeper = (org.bukkit.entity.Creeper) entity;
            creeper.setPowered(false);
            entity.setSilent(true);
        } else if (entity instanceof org.bukkit.entity.Enderman) {
            org.bukkit.entity.Enderman enderman = (org.bukkit.entity.Enderman) entity;
            enderman.setCarriedBlock(null); // No carrying blocks
            entity.setSilent(true);
        } else if (entity instanceof org.bukkit.entity.Blaze) {
            entity.setSilent(true);
        } else if (entity instanceof org.bukkit.entity.Ghast) {
            entity.setSilent(true);
        } else if (entity instanceof org.bukkit.entity.Spider) {
            entity.setSilent(true);
        } else if (entity instanceof org.bukkit.entity.Wither) {
            org.bukkit.entity.Wither wither = (org.bukkit.entity.Wither) entity;
            wither.setInvulnerable(true); // extra protection for wither
            entity.setSilent(true);
        } else if (entity instanceof org.bukkit.entity.EnderDragon) {
            org.bukkit.entity.EnderDragon dragon = (org.bukkit.entity.EnderDragon) entity;
            dragon.setInvulnerable(true); // extra protection for dragon
            entity.setSilent(true);
        }
        // Animals are naturally peaceful, just make them silent
        if (entity instanceof org.bukkit.entity.Animals) {
            entity.setSilent(true);
        }
    }

    /**
     * Removes unwanted AI goals while keeping head tracking
     */
    private void removeUnwantedAIGoals(LivingEntity entity) {
        try {
            if (entity instanceof org.bukkit.entity.Mob) {
                org.bukkit.entity.Mob mob = (org.bukkit.entity.Mob) entity;

                // Clear pathfinder goals to prevent movement
                mob.getPathfinder().stopPathfinding();

                // Set target to null to prevent attacking
                mob.setTarget(null);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to remove AI goals for " + entity.getType() + ": " + e.getMessage());
        }
    }

    /**
     * Makes entity completely immobile and unpushable
     */
    private void makeEntityImmobile(LivingEntity entity) {
        try {
            // Set velocity to zero
            entity.setVelocity(new org.bukkit.util.Vector(0, 0, 0));

            // Prevent knockback
            if (entity instanceof org.bukkit.entity.Mob) {
                org.bukkit.entity.Mob mob = (org.bukkit.entity.Mob) entity;
                mob.setCollidable(false); // Cannot be pushed by entities
            }

            // Store original location for teleport-back system
            final org.bukkit.Location originalLocation = entity.getLocation().clone();

            // Start position monitoring task
            org.bukkit.Bukkit.getScheduler().runTaskTimer(plugin, () -> {
                if (entity == null || entity.isDead()) {
                    return;
                }

                org.bukkit.Location currentLocation = entity.getLocation();

                // Check if entity moved more than 0.1 blocks from original position
                if (originalLocation.distance(currentLocation) > 0.1) {
                    // Teleport back to original position
                    entity.teleport(originalLocation);
                    entity.setVelocity(new org.bukkit.util.Vector(0, 0, 0));
                }

            }, 1L, 1L); // Check every tick

        } catch (Exception e) {
            plugin.getLogger().warning("Failed to make entity immobile: " + e.getMessage());
        }
    }

    /**
     * Starts smooth AI behavior for looking at nearby players
     */
    private void startSmoothLookAtPlayerBehavior(LivingEntity entity) {
        Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (entity == null || entity.isDead()) {
                return;
            }

            // Find nearest player within 6 blocks
            Player nearestPlayer = null;
            double minDistance = 6.0;

            for (Player player : entity.getWorld().getPlayers()) {
                if (player.getGameMode() == org.bukkit.GameMode.SPECTATOR) {
                    continue; // Skip spectators
                }

                double distance = player.getLocation().distance(entity.getLocation());
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestPlayer = player;
                }
            }

            // Look at the nearest player smoothly
            if (nearestPlayer != null) {
                Location playerLoc = nearestPlayer.getEyeLocation();
                Location entityLoc = entity.getEyeLocation();

                // Calculate direction vector
                double dx = playerLoc.getX() - entityLoc.getX();
                double dy = playerLoc.getY() - entityLoc.getY();
                double dz = playerLoc.getZ() - entityLoc.getZ();

                // Calculate yaw and pitch
                double yaw = Math.atan2(-dx, dz) * 180.0 / Math.PI;
                double pitch = Math.atan2(-dy, Math.sqrt(dx * dx + dz * dz)) * 180.0 / Math.PI;

                // Get current rotation
                Location currentLoc = entity.getLocation();
                float currentYaw = currentLoc.getYaw();
                float currentPitch = currentLoc.getPitch();

                // Smooth rotation - only rotate if difference is significant
                float yawDiff = (float) yaw - currentYaw;
                float pitchDiff = (float) pitch - currentPitch;

                // Normalize yaw difference
                while (yawDiff > 180) yawDiff -= 360;
                while (yawDiff < -180) yawDiff += 360;

                // Only update if difference is significant (reduce jittery movement)
                if (Math.abs(yawDiff) > 2 || Math.abs(pitchDiff) > 2) {
                    // Smooth interpolation - faster response
                    float newYaw = currentYaw + yawDiff * 0.3f;
                    float newPitch = currentPitch + pitchDiff * 0.3f;

                    // Create new location with same position but updated rotation
                    Location newLoc = currentLoc.clone();
                    newLoc.setYaw(newYaw);
                    newLoc.setPitch(newPitch);

                    // Use teleport to update rotation
                    entity.teleport(newLoc);

                    // Also try to set head rotation directly if possible
                    try {
                        if (entity instanceof org.bukkit.entity.Mob) {
                            org.bukkit.entity.Mob mob = (org.bukkit.entity.Mob) entity;
                            mob.lookAt(nearestPlayer.getEyeLocation());
                        }
                    } catch (Exception ignored) {
                        // Fallback to teleport only
                    }
                }
            }

        }, 20L, 3L); // Start after 1 second, run every 3 ticks (6.7 times per second)
    }

    /**
     * Monitors entity position and prevents movement
     */
    private void startPositionMonitoring(LivingEntity entity, SoulShopkeeper shopkeeper) {
        // Store the original spawn location
        Location originalLocation = shopkeeper.getLocation().clone();

        Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (entity == null || entity.isDead()) {
                return;
            }

            Location currentLocation = entity.getLocation();

            // Check if entity has moved from original position
            if (currentLocation.distance(originalLocation) > 0.1) {
                // Teleport back to original position
                Location resetLocation = originalLocation.clone();
                resetLocation.setYaw(currentLocation.getYaw()); // Keep current rotation
                resetLocation.setPitch(currentLocation.getPitch());

                entity.teleport(resetLocation);
            }

        }, 5L, 5L); // Check every 0.25 seconds for immediate correction
    }

    /**
     * Gets the shopkeeper ID from an entity
     */
    public java.util.UUID getShopkeeperIdFromEntity(Entity entity) {
        if (!isShopkeeperEntity(entity)) {
            return null;
        }

        try {
            String idString = entity.getMetadata("soulshopkeeper_id").get(0).asString();
            return java.util.UUID.fromString(idString);
        } catch (Exception e) {
            return null;
        }
    }



    /**
     * Shutdown cleanup
     */
    public void shutdown() {
        plugin.getLogger().info("Starting entity manager shutdown...");

        // Cancel all running tasks first
        cancelAllTasks();

        // Remove all shopkeeper entities
        removeAllShopkeeperEntities();

        // Clear all tracking maps
        spawnedEntities.clear();

        // Force cleanup any remaining entities in all worlds
        forceCleanupAllWorlds();

        plugin.getLogger().info("Entity manager shutdown completed.");
    }

    /**
     * Cancels all running tasks
     */
    public void cancelAllTasks() {
        try {
            // Cancel all scheduled tasks for this plugin
            org.bukkit.Bukkit.getScheduler().cancelTasks(plugin);
            plugin.getLogger().info("All scheduled tasks cancelled.");
        } catch (Exception e) {
            plugin.getLogger().warning("Error cancelling tasks: " + e.getMessage());
        }
    }

    /**
     * Force cleanup all shopkeeper entities in all worlds
     */
    private void forceCleanupAllWorlds() {
        try {
            int removed = 0;
            for (org.bukkit.World world : org.bukkit.Bukkit.getWorlds()) {
                // Create a copy to avoid concurrent modification
                java.util.List<org.bukkit.entity.Entity> entities = new java.util.ArrayList<>(world.getEntities());
                for (org.bukkit.entity.Entity entity : entities) {
                    if (isShopkeeperEntityExtended(entity)) {
                        plugin.getLogger().info("Force removing shopkeeper entity: " + entity.getType() +
                                               " at " + entity.getLocation() +
                                               " with metadata: " + getEntityMetadataInfo(entity));
                        entity.remove();
                        removed++;
                    }
                }
            }
            if (removed > 0) {
                plugin.getLogger().info("Force removed " + removed + " shopkeeper entities from all worlds.");
            } else {
                plugin.getLogger().info("No shopkeeper entities found to remove during force cleanup.");
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Error during force cleanup: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Extended shopkeeper entity detection with more thorough checks
     */
    private boolean isShopkeeperEntityExtended(org.bukkit.entity.Entity entity) {
        if (entity == null || entity.isDead()) {
            return false;
        }

        // Check all possible metadata keys
        String[] metadataKeys = {
            METADATA_KEY,
            "soulshopkeeper_id",
            "shopkeeper_id",
            "soul_shopkeeper",
            "SoulShopkeeper"
        };

        for (String key : metadataKeys) {
            if (entity.hasMetadata(key)) {
                return true;
            }
        }

        // Check if entity is in our tracking map
        if (spawnedEntities.containsValue(entity)) {
            return true;
        }

        // Check custom name patterns
        String customName = entity.getCustomName();
        if (customName != null) {
            // Check if name contains shopkeeper indicators
            if (customName.contains("Shopkeeper") ||
                customName.contains("§") || // Color codes often used in shopkeeper names
                entity.isCustomNameVisible()) {

                // Additional check: is this entity type commonly used for shopkeepers?
                org.bukkit.entity.EntityType type = entity.getType();
                if (type == org.bukkit.entity.EntityType.VILLAGER ||
                    type == org.bukkit.entity.EntityType.ZOMBIE ||
                    type == org.bukkit.entity.EntityType.SKELETON) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Gets metadata information for debugging
     */
    private String getEntityMetadataInfo(org.bukkit.entity.Entity entity) {
        java.util.List<String> metadataInfo = new java.util.ArrayList<>();

        String[] metadataKeys = {
            METADATA_KEY,
            "soulshopkeeper_id",
            "shopkeeper_id",
            "soul_shopkeeper",
            "SoulShopkeeper"
        };

        for (String key : metadataKeys) {
            if (entity.hasMetadata(key)) {
                try {
                    String value = entity.getMetadata(key).get(0).asString();
                    metadataInfo.add(key + "=" + value);
                } catch (Exception e) {
                    metadataInfo.add(key + "=<error>");
                }
            }
        }

        if (entity.getCustomName() != null) {
            metadataInfo.add("customName=" + entity.getCustomName());
        }

        return metadataInfo.isEmpty() ? "none" : String.join(", ", metadataInfo);
    }
}
