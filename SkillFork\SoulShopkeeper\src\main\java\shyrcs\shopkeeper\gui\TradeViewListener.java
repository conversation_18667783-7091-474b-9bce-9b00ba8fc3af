package shyrcs.shopkeeper.gui;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.utils.MessageUtils;

/**
 * Listener for Trade View Mode selection and navigation
 */
public class TradeViewListener implements Listener {
    
    private final SoulShopkeeperPlugin plugin;
    private final TradeViewManager tradeViewManager;
    
    public TradeViewListener(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
        this.tradeViewManager = new TradeViewManager(plugin);
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();
        
        // Handle Trade View Mode Selection
        if (title.contains("Select Trade View Mode")) {
            handleTradeViewModeClick(event, player);
            return;
        }
        
        // Handle Trade List Selection
        if (title.contains("Select Trade to View")) {
            handleTradeListClick(event, player);
            return;
        }
    }
    
    /**
     * Handles clicks in trade view mode selection GUI
     */
    private void handleTradeViewModeClick(InventoryClickEvent event, Player player) {
        event.setCancelled(true); // ALWAYS cancel to prevent item theft
        
        int slot = event.getSlot();
        
        // Get session
        ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
        if (session == null || session.getType() != ShopkeeperGUIManager.GUIType.TRADE_VIEW_MODE) {
            player.closeInventory();
            return;
        }
        
        SoulShopkeeper shopkeeper = session.getShopkeeper();
        if (shopkeeper == null) {
            player.closeInventory();
            return;
        }
        
        switch (slot) {
            case 11: // Trade GUI Mode
                player.closeInventory();
                tradeViewManager.openTradeGUISelection(player, shopkeeper);
                break;
                
            case 15: // Native Trading Mode
                player.closeInventory();
                tradeViewManager.openNativeTrading(player, shopkeeper);
                break;
                
            case 22: // Back
                player.closeInventory();
                plugin.getGUIManager().openEditGUI(player, shopkeeper);
                break;
                
            default:
                // Do nothing for other slots
                break;
        }
    }
    
    /**
     * Handles clicks in trade list selection GUI
     */
    private void handleTradeListClick(InventoryClickEvent event, Player player) {
        event.setCancelled(true); // ALWAYS cancel to prevent item theft
        
        int slot = event.getSlot();
        
        // Get session
        ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
        if (session == null || session.getType() != ShopkeeperGUIManager.GUIType.TRADE_LIST) {
            player.closeInventory();
            return;
        }
        
        SoulShopkeeper shopkeeper = session.getShopkeeper();
        if (shopkeeper == null) {
            player.closeInventory();
            return;
        }
        
        if (slot == 49) { // Back button
            player.closeInventory();
            tradeViewManager.openTradeViewSelection(player, shopkeeper);
            return;
        }
        
        // Check if clicked on a trade item
        if (event.getCurrentItem() != null && 
            event.getCurrentItem().hasItemMeta() && 
            event.getCurrentItem().getItemMeta().hasDisplayName()) {
            
            String displayName = event.getCurrentItem().getItemMeta().getDisplayName();
            if (displayName.contains("Trade #")) {
                try {
                    // Extract trade number from display name
                    String tradeNumStr = displayName.replaceAll(".*Trade #(\\d+).*", "$1");
                    int tradeIndex = Integer.parseInt(tradeNumStr) - 1;
                    
                    if (tradeIndex >= 0 && tradeIndex < shopkeeper.getTrades().size()) {
                        player.closeInventory();
                        plugin.getGUIManager().openTradeViewGUI(player, shopkeeper, tradeIndex);
                    }
                } catch (NumberFormatException e) {
                    // Invalid trade number, ignore
                }
            }
        }
    }
    
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getPlayer();
        String title = event.getView().getTitle();
        
        // Clean up sessions for trade view GUIs
        if (title.contains("Select Trade View Mode") || 
            title.contains("Select Trade to View")) {
            
            // Schedule cleanup to avoid concurrent modification
            org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
                if (session != null && 
                    (session.getType() == ShopkeeperGUIManager.GUIType.TRADE_VIEW_MODE ||
                     session.getType() == ShopkeeperGUIManager.GUIType.TRADE_LIST)) {
                    plugin.getGUIManager().removeSession(player.getUniqueId());
                }
            }, 1L);
        }
    }
}
