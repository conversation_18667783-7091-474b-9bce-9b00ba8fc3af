package shyrcs.shopkeeper.gui;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import shyrcs.shopkeeper.SoulShopkeeperPlugin;
import shyrcs.shopkeeper.gui.ShopkeeperGUIManager;
import shyrcs.shopkeeper.storage.data.SoulShopkeeper;
import shyrcs.shopkeeper.storage.data.ShopkeeperTrade;
import shyrcs.shopkeeper.utils.MessageUtils;

import java.util.List;

/**
 * Listener for Trade View Mode selection and navigation
 */
public class TradeViewListener implements Listener {
    
    private final SoulShopkeeperPlugin plugin;
    private final TradeViewManager tradeViewManager;
    
    public TradeViewListener(SoulShopkeeperPlugin plugin) {
        this.plugin = plugin;
        this.tradeViewManager = new TradeViewManager(plugin);
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        String title = event.getView().getTitle();
        
        // Handle Trade View Mode Selection
        if (title.contains("Select Trade View Mode")) {
            handleTradeViewModeClick(event, player);
            return;
        }
        
        // Handle Trade List Selection
        if (title.contains("Select Trade to View")) {
            handleTradeListClick(event, player);
            return;
        }

        // Handle GUI Trading (customer interaction)
        if (title.contains("- Trades")) {
            handleGUITradingClick(event, player);
            return;
        }
    }
    
    /**
     * Handles clicks in trade view mode selection GUI
     */
    private void handleTradeViewModeClick(InventoryClickEvent event, Player player) {
        event.setCancelled(true); // ALWAYS cancel to prevent item theft
        
        int slot = event.getSlot();
        
        // Get session
        ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
        if (session == null || session.getType() != ShopkeeperGUIManager.GUIType.TRADE_VIEW_MODE) {
            player.closeInventory();
            return;
        }
        
        SoulShopkeeper shopkeeper = session.getShopkeeper();
        if (shopkeeper == null) {
            player.closeInventory();
            return;
        }
        
        switch (slot) {
            case 11: // Trade GUI Mode
                player.closeInventory();
                tradeViewManager.openTradeGUISelection(player, shopkeeper);
                break;
                
            case 15: // Native Trading Mode
                player.closeInventory();
                tradeViewManager.openNativeTrading(player, shopkeeper);
                break;
                
            case 22: // Back
                player.closeInventory();
                plugin.getGUIManager().openEditGUI(player, shopkeeper);
                break;
                
            default:
                // Do nothing for other slots
                break;
        }
    }
    
    /**
     * Handles clicks in trade list selection GUI
     */
    private void handleTradeListClick(InventoryClickEvent event, Player player) {
        event.setCancelled(true); // ALWAYS cancel to prevent item theft
        
        int slot = event.getSlot();
        
        // Get session
        ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
        if (session == null || session.getType() != ShopkeeperGUIManager.GUIType.TRADE_LIST) {
            player.closeInventory();
            return;
        }
        
        SoulShopkeeper shopkeeper = session.getShopkeeper();
        if (shopkeeper == null) {
            player.closeInventory();
            return;
        }
        
        if (slot == 49) { // Back button
            player.closeInventory();
            tradeViewManager.openTradeViewSelection(player, shopkeeper);
            return;
        }
        
        // Check if clicked on a trade item
        if (event.getCurrentItem() != null && 
            event.getCurrentItem().hasItemMeta() && 
            event.getCurrentItem().getItemMeta().hasDisplayName()) {
            
            String displayName = event.getCurrentItem().getItemMeta().getDisplayName();
            if (displayName.contains("Trade #")) {
                try {
                    // Extract trade number from display name
                    String tradeNumStr = displayName.replaceAll(".*Trade #(\\d+).*", "$1");
                    int tradeIndex = Integer.parseInt(tradeNumStr) - 1;
                    
                    if (tradeIndex >= 0 && tradeIndex < shopkeeper.getTrades().size()) {
                        player.closeInventory();
                        plugin.getGUIManager().openTradeViewGUI(player, shopkeeper, tradeIndex);
                    }
                } catch (NumberFormatException e) {
                    // Invalid trade number, ignore
                }
            }
        }
    }

    /**
     * Handles clicks in GUI trading interface (customer interaction)
     */
    private void handleGUITradingClick(InventoryClickEvent event, Player player) {
        event.setCancelled(true); // ALWAYS cancel to prevent item theft

        int slot = event.getSlot();

        // Get session
        ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
        if (session == null || session.getType() != ShopkeeperGUIManager.GUIType.TRADE_LIST) {
            player.closeInventory();
            return;
        }

        SoulShopkeeper shopkeeper = session.getShopkeeper();
        if (shopkeeper == null) {
            player.closeInventory();
            return;
        }

        // Check if clicked on a trade item (slots 10-43, excluding borders)
        if (slot >= 10 && slot <= 43 && slot % 9 != 0 && slot % 9 != 8) {
            org.bukkit.inventory.ItemStack clickedItem = event.getCurrentItem();
            if (clickedItem != null &&
                clickedItem.getType() != org.bukkit.Material.GRAY_STAINED_GLASS_PANE &&
                clickedItem.hasItemMeta() &&
                clickedItem.getItemMeta().hasLore()) {

                // Extract trade index from lore
                List<String> lore = clickedItem.getItemMeta().getLore();
                for (String line : lore) {
                    if (line.contains("Trade #")) {
                        try {
                            String tradeNumStr = line.replaceAll(".*Trade #(\\d+).*", "$1");
                            int tradeIndex = Integer.parseInt(tradeNumStr) - 1;

                            if (tradeIndex >= 0 && tradeIndex < shopkeeper.getTrades().size()) {
                                ShopkeeperTrade trade = shopkeeper.getTrades().get(tradeIndex);

                                if (event.isLeftClick()) {
                                    // Left-click: Show recipe GUI
                                    player.closeInventory();
                                    plugin.getGUIManager().openTradeViewGUI(player, shopkeeper, tradeIndex);
                                } else if (event.isRightClick()) {
                                    // Right-click: Quick trade (open native trading for this specific trade)
                                    player.closeInventory();
                                    openQuickTrade(player, shopkeeper, trade);
                                }
                                return;
                            }
                        } catch (NumberFormatException e) {
                            // Invalid trade number, ignore
                        }
                        break;
                    }
                }
            }
        }
    }

    /**
     * Opens quick trade (native trading interface for specific trade)
     */
    private void openQuickTrade(Player player, SoulShopkeeper shopkeeper, ShopkeeperTrade trade) {
        try {
            if (!trade.isEnabled()) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cThis trade is currently disabled!");
                return;
            }

            org.bukkit.inventory.ItemStack result = trade.getResultItem();
            org.bukkit.inventory.ItemStack ingredient1 = trade.getIngredient1Item();
            org.bukkit.inventory.ItemStack ingredient2 = trade.getIngredient2Item();

            if (result == null || ingredient1 == null) {
                MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                       "&cInvalid trade configuration!");
                return;
            }

            // Create merchant with single trade
            org.bukkit.inventory.Merchant merchant = org.bukkit.Bukkit.createMerchant(shopkeeper.getName());
            org.bukkit.inventory.MerchantRecipe recipe = new org.bukkit.inventory.MerchantRecipe(result, 999);
            recipe.addIngredient(ingredient1);

            if (ingredient2 != null && ingredient2.getType() != org.bukkit.Material.AIR) {
                recipe.addIngredient(ingredient2);
            }

            merchant.setRecipes(java.util.Arrays.asList(recipe));
            player.openMerchant(merchant, true);

        } catch (Exception e) {
            plugin.getLogger().severe("Failed to open quick trade: " + e.getMessage());
            MessageUtils.sendMessage(player, plugin.getConfig().getString("messages.prefix") +
                                   "&cFailed to open trading interface!");
        }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getPlayer();
        String title = event.getView().getTitle();
        
        // Clean up sessions for trade view GUIs
        if (title.contains("Select Trade View Mode") || 
            title.contains("Select Trade to View")) {
            
            // Schedule cleanup to avoid concurrent modification
            org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                ShopkeeperGUIManager.GUISession session = plugin.getGUIManager().getSession(player.getUniqueId());
                if (session != null && 
                    (session.getType() == ShopkeeperGUIManager.GUIType.TRADE_VIEW_MODE ||
                     session.getType() == ShopkeeperGUIManager.GUIType.TRADE_LIST)) {
                    plugin.getGUIManager().removeSession(player.getUniqueId());
                }
            }, 1L);
        }
    }
}
