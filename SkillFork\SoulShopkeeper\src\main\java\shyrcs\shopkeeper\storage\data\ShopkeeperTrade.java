package shyrcs.shopkeeper.storage.data;

import org.bukkit.inventory.ItemStack;
import shyrcs.shopkeeper.SoulShopkeeperPlugin;

import java.util.Objects;

/**
 * Represents a trade in a SoulShopkeeper
 * Supports MMOItems with preserved NBT data
 */
public class ShopkeeperTrade {
    
    private String resultItemId;      // Stored MMOItem ID for result
    private String ingredient1Id;     // Stored MMOItem ID for first ingredient
    private String ingredient2Id;     // Stored MMOItem ID for second ingredient (optional)
    
    private int maxUses;             // Maximum number of uses (-1 for unlimited)
    private int uses;                // Current number of uses
    private boolean enabled;         // Whether this trade is enabled
    
    private long createdTime;
    private long lastUsed;
    
    public ShopkeeperTrade(String resultItemId, String ingredient1Id, String ingredient2Id, 
                          int maxUses, int uses, boolean enabled) {
        this.resultItemId = resultItemId;
        this.ingredient1Id = ingredient1Id;
        this.ingredient2Id = ingredient2Id;
        this.maxUses = maxUses;
        this.uses = uses;
        this.enabled = enabled;
        this.createdTime = System.currentTimeMillis();
        this.lastUsed = 0;
    }
    
    // Constructor for simple trades
    public ShopkeeperTrade(String resultItemId, String ingredient1Id) {
        this(resultItemId, ingredient1Id, null, -1, 0, true);
    }
    
    // Getters
    public String getResultItemId() {
        return resultItemId;
    }
    
    public String getIngredient1Id() {
        return ingredient1Id;
    }
    
    public String getIngredient2Id() {
        return ingredient2Id;
    }
    
    public int getMaxUses() {
        return maxUses;
    }
    
    public int getUses() {
        return uses;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public long getCreatedTime() {
        return createdTime;
    }
    
    public long getLastUsed() {
        return lastUsed;
    }
    
    // Setters
    public void setResultItemId(String resultItemId) {
        this.resultItemId = resultItemId;
    }
    
    public void setIngredient1Id(String ingredient1Id) {
        this.ingredient1Id = ingredient1Id;
    }
    
    public void setIngredient2Id(String ingredient2Id) {
        this.ingredient2Id = ingredient2Id;
    }
    
    public void setMaxUses(int maxUses) {
        this.maxUses = maxUses;
    }
    
    public void setUses(int uses) {
        this.uses = uses;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    // Trade logic methods
    
    /**
     * Checks if this trade can be used
     */
    public boolean canUse() {
        return enabled && (maxUses == -1 || uses < maxUses);
    }
    
    /**
     * Uses this trade (increments use count)
     */
    public boolean use() {
        if (!canUse()) {
            return false;
        }
        
        uses++;
        lastUsed = System.currentTimeMillis();
        return true;
    }
    
    /**
     * Gets remaining uses
     */
    public int getRemainingUses() {
        if (maxUses == -1) {
            return -1; // Unlimited
        }
        return Math.max(0, maxUses - uses);
    }
    
    /**
     * Checks if this trade is out of stock
     */
    public boolean isOutOfStock() {
        return maxUses != -1 && uses >= maxUses;
    }
    
    /**
     * Resets the use count
     */
    public void resetUses() {
        this.uses = 0;
    }
    
    // ItemStack retrieval methods
    
    /**
     * Gets the result ItemStack from stored MMOItem
     */
    public ItemStack getResultItem() {
        if (resultItemId == null) {
            return null;
        }
        
        SoulShopkeeperPlugin plugin = SoulShopkeeperPlugin.getInstance();
        if (plugin != null) {
            return plugin.getMMOItemStorage().retrieveMMOItem(resultItemId);
        }
        
        return null;
    }
    
    /**
     * Gets the first ingredient ItemStack from stored MMOItem
     */
    public ItemStack getIngredient1Item() {
        if (ingredient1Id == null) {
            return null;
        }
        
        SoulShopkeeperPlugin plugin = SoulShopkeeperPlugin.getInstance();
        if (plugin != null) {
            return plugin.getMMOItemStorage().retrieveMMOItem(ingredient1Id);
        }
        
        return null;
    }
    
    /**
     * Gets the second ingredient ItemStack from stored MMOItem
     */
    public ItemStack getIngredient2Item() {
        if (ingredient2Id == null) {
            return null;
        }
        
        SoulShopkeeperPlugin plugin = SoulShopkeeperPlugin.getInstance();
        if (plugin != null) {
            return plugin.getMMOItemStorage().retrieveMMOItem(ingredient2Id);
        }
        
        return null;
    }
    
    // Utility methods
    
    /**
     * Checks if this trade has a second ingredient
     */
    public boolean hasSecondIngredient() {
        return ingredient2Id != null && !ingredient2Id.isEmpty();
    }
    
    /**
     * Validates this trade
     */
    public boolean isValid() {
        return resultItemId != null && !resultItemId.isEmpty() && 
               ingredient1Id != null && !ingredient1Id.isEmpty();
    }
    
    /**
     * Gets a summary of this trade
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("Trade{");
        
        if (ingredient1Id != null) {
            sb.append("ingredient1=").append(ingredient1Id);
        }
        
        if (hasSecondIngredient()) {
            sb.append(", ingredient2=").append(ingredient2Id);
        }
        
        sb.append(" -> result=").append(resultItemId);
        
        if (maxUses != -1) {
            sb.append(", uses=").append(uses).append("/").append(maxUses);
        }
        
        sb.append(", enabled=").append(enabled);
        sb.append("}");
        
        return sb.toString();
    }
    
    /**
     * Creates a copy of this trade
     */
    public ShopkeeperTrade copy() {
        ShopkeeperTrade copy = new ShopkeeperTrade(resultItemId, ingredient1Id, ingredient2Id, 
                                                  maxUses, uses, enabled);
        copy.createdTime = this.createdTime;
        copy.lastUsed = this.lastUsed;
        return copy;
    }
    
    @Override
    public String toString() {
        return getSummary();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ShopkeeperTrade that = (ShopkeeperTrade) obj;
        return Objects.equals(resultItemId, that.resultItemId) &&
               Objects.equals(ingredient1Id, that.ingredient1Id) &&
               Objects.equals(ingredient2Id, that.ingredient2Id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(resultItemId, ingredient1Id, ingredient2Id);
    }

    /**
     * Sets result item from ItemStack
     */
    public void setResultItem(ItemStack item) {
        if (item == null || item.getType().isAir()) {
            this.resultItemId = null;
            return;
        }

        SoulShopkeeperPlugin plugin = SoulShopkeeperPlugin.getInstance();
        if (plugin != null) {
            this.resultItemId = plugin.getMMOItemStorage().storeMMOItem(item);
        }
    }

    /**
     * Sets ingredient1 item from ItemStack
     */
    public void setIngredient1Item(ItemStack item) {
        if (item == null || item.getType().isAir()) {
            this.ingredient1Id = null;
            return;
        }

        SoulShopkeeperPlugin plugin = SoulShopkeeperPlugin.getInstance();
        if (plugin != null) {
            this.ingredient1Id = plugin.getMMOItemStorage().storeMMOItem(item);
        }
    }

    /**
     * Sets ingredient2 item from ItemStack
     */
    public void setIngredient2Item(ItemStack item) {
        if (item == null || item.getType().isAir()) {
            this.ingredient2Id = null;
            return;
        }

        SoulShopkeeperPlugin plugin = SoulShopkeeperPlugin.getInstance();
        if (plugin != null) {
            this.ingredient2Id = plugin.getMMOItemStorage().storeMMOItem(item);
        }
    }
}
